#!/usr/bin/env python3
"""
数据库操作脚本 - 用于manage_duckdb.sh调用
支持各种数据库操作命令
"""

import sys
import os
import argparse

# pyright: reportMissingImports=false
# pylint: disable=import-error

# 添加backend路径到sys.path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
backend_path = os.path.join(project_root, 'backend')

# 确保backend路径存在
if os.path.exists(backend_path):
    sys.path.insert(0, backend_path)
else:
    print(f"错误: backend路径不存在: {backend_path}")
    sys.exit(1)

# 路径配置完成
# 注意: Pylance可能会报告导入错误，但这是正常的，因为路径是动态添加的

def show_database_info(db_path):
    """显示数据库信息"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
    
    try:
        from database.duckdb_manager import DuckDBManager
        from database.algorithm_storage_manager import AlgorithmStorageManager
        
        db_manager = DuckDBManager(db_path)
        
        # 检查新存储结构是否存在
        storage_manager = AlgorithmStorageManager(db_manager)
        new_storage_exists = storage_manager._check_new_storage_exists()
        
        print(f'存储结构: {"新存储" if new_storage_exists else "旧存储"}')
        
        # 获取所有表的统计信息
        with db_manager.get_connection() as conn:
            all_tables = conn.execute('SHOW TABLES').fetchall()
            print('表名\t\t\t记录数\t\t类型')
            print('-' * 70)
            
            # 分类显示表
            auth_tables = []
            algorithm_tables = []
            user_tables = []
            business_tables = []
            
            for table_row in sorted(all_tables):
                table = table_row[0]
                try:
                    count = conn.execute(f'SELECT COUNT(*) FROM {table}').fetchone()[0]
                    
                    if table.startswith('auth_'):
                        auth_tables.append((table, count, '🔐 认证'))
                    elif table in ['algorithm_results', 'wash_trading_results', 'same_account_wash_trading', 
                                  'cross_account_wash_trading', 'high_frequency_trading_details',
                                  'funding_rate_arbitrage_details']:
                        algorithm_tables.append((table, count, '🆕 算法'))
                    elif table in ['contract_risk_analysis', 'contract_risk_analysis_view']:
                        algorithm_tables.append((table, count, '🔄 兼容'))
                    elif table in ['users', 'user_relationships', 'user_trading_profiles']:
                        marker = '🆕 用户' if table == 'user_trading_profiles' else '✅ 用户'
                        user_tables.append((table, count, marker))
                    else:
                        business_tables.append((table, count, '📊 业务'))
                        
                except Exception as e:
                    print(f'{table:<25}\t{"错误":>8}\t{e}')
            
            # 按分类显示
            all_categorized = auth_tables + algorithm_tables + user_tables + business_tables
            for table, count, table_type in all_categorized:
                print(f'{table:<25}\t{count:>8}\t{table_type}')
                
            print(f'\n📊 总计: {len(all_tables)} 个表 (认证:{len(auth_tables)} | 算法:{len(algorithm_tables)} | 用户:{len(user_tables)} | 业务:{len(business_tables)})')
    except Exception as e:
        print(f'获取表统计失败: {e}')
        import traceback
        traceback.print_exc()
        sys.exit(1)

def execute_sql(db_path, sql):
    """执行SQL查询"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
        
    try:
        from database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager(db_path)
        result = db_manager.execute_sql(sql)
        if result:
            # 显示表头
            if len(result) > 0:
                headers = list(result[0].keys())
                print('\t'.join(headers))
                print('-' * (len('\t'.join(headers))))
                # 显示数据
                for row in result:
                    print('\t'.join(str(row[col]) for col in headers))
            print(f'查询完成，返回 {len(result)} 行记录')
        else:
            print('查询执行完成')
    except Exception as e:
        print(f'SQL执行失败: {e}')
        import traceback
        traceback.print_exc()
        sys.exit(1)

def show_tables(db_path):
    """显示所有表"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
        
    try:
        from database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager(db_path)
        result = db_manager.execute_sql('SHOW TABLES')
        if result:
            for row in result:
                print(row['name'])
        else:
            print('没有找到表')
    except Exception as e:
        print(f'获取表列表失败: {e}')
        sys.exit(1)

def describe_table(db_path, table_name):
    """显示表结构"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
        
    try:
        from database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager(db_path)
        result = db_manager.execute_sql(f'DESCRIBE {table_name}')
        if result:
            print('列名\t\t类型\t\t可空\t\t键\t\t默认值\t\t额外')
            print('-' * 80)
            for row in result:
                print(f"{row.get('column_name', '')}\t\t{row.get('column_type', '')}\t\t{row.get('null', '')}\t\t{row.get('key', '')}\t\t{row.get('default', '')}\t\t{row.get('extra', '')}")
        else:
            print('表不存在或查询失败')
    except Exception as e:
        print(f'获取表结构失败: {e}')
        sys.exit(1)

def get_table_stats(db_path):
    """获取表统计信息"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
        
    try:
        from database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager(db_path)
        stats = db_manager.get_table_stats()
        
        print('表名\t\t\t记录数')
        print('-' * 40)
        
        # 按记录数排序
        sorted_stats = sorted(stats.items(), key=lambda x: x[1], reverse=True)
        for table, count in sorted_stats:
            print(f'{table:<20}\t{count:>10}')
            
    except Exception as e:
        print(f'获取表统计失败: {e}')
        sys.exit(1)

def clear_all_data(db_path):
    """清空所有数据表"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
        
    try:
        from database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager(db_path)
        db_manager.clear_all_data()
        print('所有数据表已清空')
    except Exception as e:
        print(f'清空数据失败: {e}')
        sys.exit(1)

def check_storage_structure(db_path):
    """检查存储结构状态"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
        
    try:
        from database.duckdb_manager import DuckDBManager
        from database.algorithm_storage_manager import AlgorithmStorageManager
        
        db_manager = DuckDBManager(db_path)
        storage_manager = AlgorithmStorageManager(db_manager)
        
        # 检查新存储结构
        new_storage_exists = storage_manager._check_new_storage_exists()
        print(f'新存储结构: {"已存在" if new_storage_exists else "未初始化"}')
        
        # 检查各表状态
        stats = db_manager.get_table_stats()
        
        print('\n=== 🔄 兼容性存储表 ===')
        old_tables = ['contract_risk_analysis']
        for table in old_tables:
            count = stats.get(table, 0)
            status = '存在' if table in stats else '不存在'
            print(f'{table}: {status} ({count} 条记录) - 兼容性适配器')
        
        print('\n=== 🆕 新算法存储表 ===')
        algorithm_tables = ['algorithm_results', 'wash_trading_results', 
                           'same_account_wash_trading', 'cross_account_wash_trading',
                           'high_frequency_trading_details', 'funding_rate_arbitrage_details']
        for table in algorithm_tables:
            count = stats.get(table, 0)
            status = '✅ 存在' if table in stats else '❌ 不存在'
            print(f'{table}: {status} ({count} 条记录)')
            
        print('\n=== 👤 用户数据表 ===')
        user_tables = ['users', 'user_relationships', 'user_trading_profiles']
        for table in user_tables:
            count = stats.get(table, 0)
            status = '✅ 存在' if table in stats else '❌ 不存在'
            marker = '🆕' if table == 'user_trading_profiles' else '✅'
            print(f'{marker} {table}: {status} ({count} 条记录)')
            
        print('\n=== 📊 业务管理表 ===')
        business_tables = ['tasks', 'agent_analysis', 'shared_relationships']
        for table in business_tables:
            count = stats.get(table, 0)
            status = '✅ 存在' if table in stats else '❌ 不存在'
            print(f'{table}: {status} ({count} 条记录)')
            
    except Exception as e:
        print(f'检查失败: {e}')
        sys.exit(1)

def init_new_storage(db_path):
    """初始化新存储结构"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
        
    try:
        from database.duckdb_manager import DuckDBManager
        from database.init_new_storage import initialize_new_storage
        
        db_manager = DuckDBManager(db_path)
        success = initialize_new_storage()
        if success:
            print('新存储结构初始化完成')
        else:
            print('新存储结构初始化失败')
            sys.exit(1)
    except Exception as e:
        print(f'初始化失败: {e}')
        sys.exit(1)

def show_new_storage_statistics(db_path):
    """显示新存储的统计信息"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)

    try:
        from database.duckdb_manager import DuckDBManager
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter

        db_manager = DuckDBManager(db_path)
        adapter = ContractDataAdapter(db_manager)

        print('🆕 新存储统计信息:')
        print('=' * 50)

        # 检查新存储是否启用
        is_enabled = adapter.is_new_storage_enabled()
        print(f'新存储状态: {"✅ 已启用" if is_enabled else "❌ 未启用"}')

        if not is_enabled:
            print('请先初始化新存储结构')
            return

        # 获取统计信息
        stats = adapter.get_statistics()
        if stats:
            print(f'\n📊 分析统计:')
            print(f'  总分析次数: {stats.get("total_analyses", 0)}')
            print(f'  分析合约数: {stats.get("total_contracts_analyzed", 0)}')
            print(f'  发现风险数: {stats.get("total_risks_found", 0)}')
            print(f'  对敲交易数: {stats.get("total_wash_trading", 0)}')
            print(f'  跨BD交易数: {stats.get("total_cross_bd", 0)}')
            print(f'  平均风险率: {stats.get("avg_risk_rate", 0):.2f}%')
        else:
            print('📊 暂无统计数据')

        # 获取最近的分析结果
        recent_results = adapter.get_all_analysis_results(limit=5)
        if recent_results:
            print(f'\n📋 最近5次分析:')
            for i, result in enumerate(recent_results, 1):
                print(f'  {i}. {result.get("task_id", "N/A")[:12]}... - '
                      f'{result.get("total_contracts", 0)}合约, '
                      f'{result.get("risk_contracts", 0)}风险')
        else:
            print('📋 暂无分析记录')

        # 显示存储对比
        print(f'\n🔄 存储对比:')

        # 老存储统计
        try:
            old_stats = db_manager.execute_sql("""
                SELECT
                    COUNT(*) as count,
                    SUM(total_contracts) as total_contracts,
                    SUM(risk_contracts) as total_risks
                FROM contract_risk_analysis
            """)

            if old_stats and old_stats[0]:
                old_data = old_stats[0]
                print(f'  🔄 兼容性存储: {old_data.get("count", 0)}条记录, '
                      f'{old_data.get("total_contracts", 0)}合约, '
                      f'{old_data.get("total_risks", 0)}风险')
            else:
                print(f'  🔄 兼容性存储: 无数据')

        except Exception as e:
            print(f'  🔄 兼容性存储: 查询失败 ({e})')

        # 新存储统计
        try:
            new_stats = db_manager.execute_sql("""
                SELECT
                    COUNT(DISTINCT ar.task_id) as analyses,
                    COUNT(*) as total_records
                FROM algorithm_results ar
                WHERE ar.algorithm_type = 'suspected_wash_trading'
            """)

            if new_stats and new_stats[0]:
                new_data = new_stats[0]
                print(f'  🆕 新存储: {new_data.get("analyses", 0)}次分析, '
                      f'{new_data.get("total_records", 0)}条算法记录')
            else:
                print(f'  🆕 新存储: 无数据')

        except Exception as e:
            print(f'  🆕 新存储: 查询失败 ({e})')

    except Exception as e:
        print(f'显示统计信息失败: {e}')
        sys.exit(1)

def clean_empty_tables(db_path):
    """清理空表"""
    # 确保使用绝对路径
    if not os.path.isabs(db_path):
        db_path = os.path.abspath(db_path)
        
    try:
        from database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager(db_path)
        stats = db_manager.get_table_stats()
        
        empty_tables = [table for table, count in stats.items() if count == 0]
        
        if empty_tables:
            print(f'发现空表: {empty_tables}')
            for table in empty_tables:
                if table == 'cross_bd_wash_trading':  # 删除这个旧表
                    db_manager.execute_sql(f'DROP TABLE IF EXISTS {table}')
                    print(f'已删除空表: {table}')
        else:
            print('没有发现空表')
            
    except Exception as e:
        print(f'清理失败: {e}')
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='数据库操作工具')
    parser.add_argument('operation', help='操作类型')
    parser.add_argument('db_path', help='数据库路径')
    parser.add_argument('--sql', help='SQL查询语句')
    parser.add_argument('--table', help='表名')
    
    args = parser.parse_args()
    
    try:
        if args.operation == 'info':
            show_database_info(args.db_path)
        elif args.operation == 'sql':
            if not args.sql:
                print("执行SQL需要提供 --sql 参数")
                sys.exit(1)
            execute_sql(args.db_path, args.sql)
        elif args.operation == 'tables':
            show_tables(args.db_path)
        elif args.operation == 'describe':
            if not args.table:
                print("查看表结构需要提供 --table 参数")
                sys.exit(1)
            describe_table(args.db_path, args.table)
        elif args.operation == 'stats':
            get_table_stats(args.db_path)
        elif args.operation == 'clear':
            clear_all_data(args.db_path)
        elif args.operation == 'check_storage':
            check_storage_structure(args.db_path)
        elif args.operation == 'init_storage':
            init_new_storage(args.db_path)
        elif args.operation == 'clean_empty':
            clean_empty_tables(args.db_path)
        elif args.operation == 'new_storage_stats':
            show_new_storage_statistics(args.db_path)
        else:
            print(f"不支持的操作: {args.operation}")
            print("可用操作: info, sql, tables, describe, stats, clear, check_storage, init_storage, clean_empty, new_storage_stats")
            sys.exit(1)
            
    except ImportError as e:
        print(f'模块导入失败: {e}')
        print(f'当前Python路径: {sys.path}')
        print(f'Backend路径: {backend_path}')
        print(f'Backend路径存在: {os.path.exists(backend_path)}')
        sys.exit(1)
    except Exception as e:
        print(f'操作失败: {e}')
        sys.exit(1)

if __name__ == "__main__":
    main() 