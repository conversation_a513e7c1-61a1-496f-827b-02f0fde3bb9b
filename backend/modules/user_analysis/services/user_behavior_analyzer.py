"""
用户行为分析器
整合所有分析组件，提供完整的用户交易行为分析功能
"""

import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import yaml

from modules.user_analysis.models.user_behavior_models import (
    PositionData, UserBehaviorProfile, BasicMetrics, DerivedMetrics,
    CoinWinRateAnalysis, ProfessionalScores,  # HedgeStatistics已移除
    TradingPreferences, AbnormalAnalysis, FundScale
)
from modules.user_analysis.services.basic_metrics_calculator import BasicMetricsCalculator
from modules.user_analysis.services.derived_metrics_calculator import DerivedMetricsCalculator
from modules.user_analysis.services.coin_win_rate_analyzer import CoinWinRateAnalyzer
from modules.user_analysis.services.professionalism_scorer import ProfessionalismScorer

logger = logging.getLogger(__name__)


class UserBehaviorAnalyzer:
    """用户行为分析器 - 主要分析引擎"""
    
    def __init__(self, config_path: str = None):
        """
        初始化用户行为分析器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.config = self._load_config(config_path)
        
        # 初始化各个分析器
        self.basic_calculator = BasicMetricsCalculator(self.config)
        self.derived_calculator = DerivedMetricsCalculator(self.config)
        self.coin_analyzer = CoinWinRateAnalyzer(self.config)
        # self.hedge_calculator = HedgeDisplayCalculator(self.config)  # 对冲计算器已移除
        self.professionalism_scorer = ProfessionalismScorer(self.config)
        
        logger.debug("用户行为分析器初始化完成")
    
    def analyze_user_behavior(self, user_id: str, positions: List[PositionData]) -> UserBehaviorProfile:
        """
        分析用户交易行为
        
        Args:
            user_id: 用户ID
            positions: 持仓数据列表
            
        Returns:
            UserBehaviorProfile: 完整的分析结果
        """
        try:
            # 数据质量验证
            is_valid, validation_message = self.basic_calculator.validate_data_quality(positions)
            if not is_valid:
                logger.debug(f"用户 {user_id}: 数据质量验证失败: {validation_message}")
                return self._create_empty_result(user_id, validation_message, positions)
            
            # 1. 计算基础指标
            basic_metrics = self.basic_calculator.calculate_basic_metrics(positions)
            
            # 1.1. 计算扩展指标并整合到基础指标中
            extended_metrics = self.basic_calculator.calculate_extended_metrics(positions)
            
            # 🚀 将扩展指标数据整合到基础指标中
            basic_metrics.max_trade_amount = extended_metrics.get('max_trade_amount', 0.0)
            basic_metrics.max_trade_contract = extended_metrics.get('max_trade_contract', '')
            basic_metrics.min_trade_amount = extended_metrics.get('min_trade_amount', 0.0)
            basic_metrics.min_trade_contract = extended_metrics.get('min_trade_contract', '')
            basic_metrics.coin_preference_distribution = extended_metrics.get('coin_preference_distribution', {})
            basic_metrics.time_distribution = extended_metrics.get('time_distribution', {})
            basic_metrics.leverage_distribution = extended_metrics.get('leverage_distribution', {})
            basic_metrics.order_type_distribution = extended_metrics.get('order_type_distribution', {})
            basic_metrics.position_size_distribution = extended_metrics.get('position_size_distribution', {})
            
            # 🚀 新增：映射额外的扩展指标字段
            basic_metrics.max_holding_time = extended_metrics.get('max_holding_time', 0.0)
            basic_metrics.min_holding_time = extended_metrics.get('min_holding_time', 0.0)
            basic_metrics.low_leverage_trades = extended_metrics.get('low_leverage_trades', 0)
            basic_metrics.medium_leverage_trades = extended_metrics.get('medium_leverage_trades', 0)
            basic_metrics.high_leverage_trades = extended_metrics.get('high_leverage_trades', 0)
            basic_metrics.fee_ratio = extended_metrics.get('fee_ratio', 0.0)
            basic_metrics.defi_percentage = extended_metrics.get('defi_percentage', 0.0)
            basic_metrics.others_percentage = extended_metrics.get('others_percentage', 0.0)
            
            # 🚀 新增：将额外的扩展指标映射到BasicMetrics
            # 时间指标扩展
            basic_metrics.max_holding_time = extended_metrics.get('max_holding_time', 0.0)
            basic_metrics.min_holding_time = extended_metrics.get('min_holding_time', 0.0)
            
            # 杠杆分布扩展  
            basic_metrics.low_leverage_trades = extended_metrics.get('low_leverage_trades', 0)
            basic_metrics.medium_leverage_trades = extended_metrics.get('medium_leverage_trades', 0)
            basic_metrics.high_leverage_trades = extended_metrics.get('high_leverage_trades', 0)
            
            # 订单类型扩展
            basic_metrics.open_limit_orders = extended_metrics.get('open_limit_orders', 0)
            basic_metrics.close_limit_orders = extended_metrics.get('close_limit_orders', 0)
            
            # 币种偏好扩展
            basic_metrics.defi_percentage = extended_metrics.get('defi_percentage', 0.0)
            basic_metrics.others_percentage = extended_metrics.get('others_percentage', 0.0)
            
            # 费率指标
            basic_metrics.fee_ratio = extended_metrics.get('fee_ratio', 0.0)
            
            # 2. 计算衍生指标
            derived_metrics = self.derived_calculator.calculate_derived_metrics(basic_metrics, positions)
            
            # 3. 币种胜率分析
            coin_analysis = self.coin_analyzer.analyze_coin_performance(positions)
            
            # 4. 对冲数据统计（已移除）
            # hedge_statistics = None
            #     hedge_statistics = self.hedge_calculator.calculate_hedge_statistics(positions)

            # 5. 专业度评分
            professionalism_score = self.professionalism_scorer.calculate_professionalism_score(
                basic_metrics, derived_metrics, coin_analysis, None  # hedge_statistics已移除
            )
            
            # 6. 确定资金规模分类
            fund_scale_category = self._classify_fund_scale(basic_metrics.total_volume)
            
            # 6.1. 计算交易偏好分析
            trading_preferences = self._calculate_trading_preferences(positions, basic_metrics)
            
            # 6.2. 计算异常交易分析
            abnormal_analysis = self._calculate_abnormal_analysis(user_id, positions, basic_metrics)
            
            # 7. 生成综合分析结果
            now = datetime.now()

            # 🚀 修复: 确保 analysis_period_end 永远不为 None
            analysis_period_end = now
            if positions:
                # 找到最后一个有效的 close_time
                valid_close_times = [pos.close_time for pos in positions if pos.close_time is not None]
                if valid_close_times:
                    analysis_period_end = max(valid_close_times)
                else:
                    # 如果没有有效的 close_time，使用最后一个 open_time + 15分钟作为估算
                    from datetime import timedelta
                    last_open_time = max(pos.open_time for pos in positions)
                    analysis_period_end = last_open_time + timedelta(minutes=15)

            analysis_result = UserBehaviorProfile(
                user_id=user_id,
                analysis_timestamp=now,
                analysis_period_start=positions[0].open_time if positions else now,
                analysis_period_end=analysis_period_end,
                basic_metrics=basic_metrics,
                derived_metrics=derived_metrics,
                professional_scores=professionalism_score,
                coin_win_rate_analysis=coin_analysis,
                # hedge_statistics=hedge_statistics,  # 对冲统计已移除
                trading_preferences=trading_preferences,
                abnormal_analysis=abnormal_analysis,
                fund_scale_category=fund_scale_category,
                data_quality_score=1.0,
                analysis_confidence=0.95
            )

            # 🚀 新增: 保存positions数据以便后续保存时使用
            analysis_result._positions = positions
            
            return analysis_result
            
        except Exception as e:
            logger.debug(f"分析用户 {user_id} 行为时发生错误: {str(e)}")
            return self._create_empty_result(user_id, f"分析失败: {str(e)}")
    
    def get_analyzer_status(self) -> Dict:
        """获取分析器状态"""
        return {
            'status': 'active',
            'config_version': '1.0.0',
            'initialized_at': datetime.now().isoformat(),
            'components': {
                'basic_calculator': 'active',
                'derived_calculator': 'active',
                'coin_analyzer': 'active',
                # 'hedge_calculator': 'active',  # 对冲计算器已移除
                'professionalism_scorer': 'active'
            },
            'config_loaded': self.config is not None,
            'total_components': 4  # 减少1个组件（对冲计算器已移除）
        }
    
    def update_config(self, new_config: Dict) -> bool:
        """更新配置"""
        try:
            # 这里可以添加配置验证逻辑
            self.config.update(new_config)
            logger.info("配置更新成功")
            return True
        except Exception as e:
            logger.error(f"配置更新失败: {str(e)}")
            return False
    
    def _classify_fund_scale(self, total_volume: float) -> str:
        """分类资金规模"""
        fund_scale_thresholds = self.config.get('fund_scale_thresholds', {
            'large': 500000,
            'medium': 100000, 
            'retail': 20000
        })
        
        if total_volume >= fund_scale_thresholds.get('large', 500000):
            return FundScale.LARGE.value
        elif total_volume >= fund_scale_thresholds.get('medium', 100000):
            return FundScale.MEDIUM.value
        elif total_volume >= fund_scale_thresholds.get('retail', 20000):
            return FundScale.RETAIL.value
        else:
            return FundScale.SMALL.value
    
    def _load_config(self, config_path: str = None) -> Dict:
        """加载配置文件"""
        if config_path is None:
            # 使用绝对路径确保能找到配置文件
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, "..", "..", "..", "config", "user_behavior_config.yml")
            config_path = os.path.normpath(config_path)

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                full_config = yaml.safe_load(f)
            # 提取user_behavior_analysis部分的配置
            config = full_config.get('user_behavior_analysis', {})
            logger.debug(f"配置文件加载成功: {config_path}")
            logger.debug(f"加载的阈值配置: {config.get('thresholds', {})}")
            return config
        except Exception as e:
            logger.warning(f"配置文件加载失败: {str(e)}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'thresholds': {
                'min_total_volume': 10000,
                'min_trades_count': 10
            },
            'scoring_weights': {
                'profitability': 0.40,
                'risk_control': 0.25,
                'trading_behavior': 0.20,
                'market_understanding': 0.15
            },
            'trader_classification': {
                'professional': {
                    'min_score': 80,
                    'min_volume': 100000,
                    'min_trades': 50,
                    'min_expert_coins': 2
                },
                'semi_professional': {
                    'min_score': 60,
                    'min_volume': 50000,
                    'min_trades': 30
                },
                'new_user': {
                    'max_trades': 20,
                    'max_volume': 20000
                }
            },
            'fund_scale_thresholds': {
                'large': 500000,
                'medium': 100000,
                'retail': 20000
            },
            'coin_win_rate_analysis': {
                'min_trades_threshold': 10,
                'min_volume_threshold': 5000,
                'expertise_levels': {
                    'expert': {'min_win_rate': 0.7, 'min_trades': 20, 'min_volume': 50000},
                    'skilled': {'min_win_rate': 0.6, 'min_trades': 15, 'min_volume': 20000},
                    'average': {'min_win_rate': 0.45, 'min_trades': 10, 'min_volume': 5000}
                },
                'ranking_weights': {
                    'win_rate': 0.4,
                    'profit_factor': 0.3,
                    'volume': 0.2,
                    'trade_count': 0.1
                },
                'advantage_identification': {
                    'high_win_rate': 0.65,
                    'high_profit_factor': 2.0,
                    'min_statistical_confidence': 0.7
                }
            },
            # 'hedge_display': {  # 对冲显示配置已移除
            #     'enabled': True,
            #     'min_time_overlap_minutes': 30
            # }
        }
    
    def _calculate_trading_preferences(self, positions: List, basic_metrics) -> TradingPreferences:
        """计算交易偏好分析"""
        from modules.user_analysis.models.user_behavior_models import TradingPreferences
        
        if not positions:
            return TradingPreferences()
        
        # 🚀 修改：计算24小时交易分布统计
        time_distribution = {}
        hour_counts = {}
        hourly_trading_distribution = {}  # 新增：24小时分布字典

        for hour in range(24):
            time_distribution[f"{hour:02d}"] = 0
            hour_counts[hour] = 0
            hourly_trading_distribution[str(hour)] = 0  # 新增：初始化24小时分布

        # 统计每小时的交易次数
        for position in positions:
            if hasattr(position, 'open_time') and position.open_time:
                hour = position.open_time.hour
                time_distribution[f"{hour:02d}"] += 1
                hour_counts[hour] += 1
                hourly_trading_distribution[str(hour)] += 1  # 新增：更新24小时分布

        # 找出交易最活跃的时段（保留原有逻辑用于兼容性）
        sorted_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)
        peak_hours_list = [f"{hour:02d}:00-{(hour+1)%24:02d}:00" for hour, count in sorted_hours[:3] if count > 0]
        legacy_peak_trading_hours = ', '.join(peak_hours_list)  # 重命名为legacy格式
        
        # 生成24小时热力图数据 (7天 x 24小时)
        heatmap_grid = []
        for day in range(7):  # 0-6 代表周一到周日
            day_data = []
            for hour in range(24):
                # 简化处理：使用小时分布数据，实际应该按周几统计
                value = hour_counts.get(hour, 0)
                day_data.append(value)
            heatmap_grid.append(day_data)
        
        # 计算风险偏好
        avg_leverage = getattr(basic_metrics, 'avg_leverage', 1.0)
        if avg_leverage <= 2:
            risk_appetite = 'conservative'
        elif avg_leverage <= 5:
            risk_appetite = 'moderate'
        elif avg_leverage <= 10:
            risk_appetite = 'aggressive'
        else:
            risk_appetite = 'high_risk'
        
        # 计算波动率偏好
        avg_duration = sum(getattr(p, 'duration_minutes', 0) for p in positions) / len(positions)
        if avg_duration < 5:
            volatility_preference = 'very_high'
        elif avg_duration < 30:
            volatility_preference = 'high'
        elif avg_duration < 240:
            volatility_preference = 'medium'
        else:
            volatility_preference = 'low'
        
        # 计算分散化程度
        contracts = set(getattr(p, 'contract_name', '') for p in positions)
        diversification_score = min(len(contracts) / 10.0, 1.0)
        
        # 获取偏好合约
        contract_counts = {}
        for position in positions:
            contract = getattr(position, 'contract_name', '')
            if contract:
                contract_counts[contract] = contract_counts.get(contract, 0) + 1
        
        favorite_contracts = sorted(contract_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        favorite_contracts = [contract for contract, _ in favorite_contracts]
        
        return TradingPreferences(
            favorite_contracts=favorite_contracts,
            peak_trading_hours=hourly_trading_distribution,  # 🚀 修改：使用新的24小时分布格式
            time_distribution=time_distribution,
            risk_appetite=risk_appetite,
            volatility_preference=volatility_preference,
            diversification_score=diversification_score,
            heatmap_grid=heatmap_grid,
            peak_hours=peak_hours_list,
            legacy_peak_trading_hours=legacy_peak_trading_hours  # 🚀 新增：保留原有格式用于兼容性
        )
    
    def _calculate_abnormal_analysis(self, user_id: str, positions: List, basic_metrics) -> AbnormalAnalysis:
        """计算异常交易分析"""
        from modules.user_analysis.models.user_behavior_models import AbnormalAnalysis
        from database.duckdb_manager import DuckDBManager
        
        try:
            # 🔧 修复版：增加数据库重试机制和异常处理
            results = []
            max_retries = 3
            
            for attempt in range(max_retries):
                try:
                    # 初始化数据库管理器
                    db_manager = DuckDBManager()
                    
                    # 先检查表是否存在（防御性检查）
                    table_check = db_manager.execute_sql(
                        "SELECT COUNT(*) as table_exists FROM information_schema.tables WHERE table_name = 'contract_risk_details'"
                    )
                    
                    if not table_check or table_check[0]['table_exists'] == 0:
                        logger.warning(f"用户 {user_id}: contract_risk_details表不存在，跳过异常交易分析")
                        results = []
                        break
                    
                    # 查询用户的异常交易记录
                    abnormal_query = """
                    SELECT 
                        detection_type,
                        COUNT(*) as count,
                        SUM(abnormal_volume) as volume,
                        AVG(risk_score) as avg_score
                    FROM contract_risk_details 
                    WHERE member_id = ?
                    GROUP BY detection_type
                    """
                    
                    results = db_manager.execute_sql(abnormal_query, [user_id])
                    logger.debug(f"用户 {user_id}: 查询到 {len(results)} 条异常交易记录")
                    break  # 成功查询，跳出重试循环
                    
                except Exception as db_error:
                    logger.warning(f"用户 {user_id}: 数据库查询失败 (尝试 {attempt + 1}/{max_retries}): {db_error}")
                    
                    if attempt == max_retries - 1:
                        # 最后一次重试失败，使用默认值
                        logger.error(f"用户 {user_id}: 数据库查询最终失败，使用默认异常分析结果")
                        results = []
                    else:
                        # 等待后重试
                        import time
                        time.sleep(1)
            
            wash_trading_volume = 0.0
            wash_trading_count = 0
            high_frequency_volume = 0.0
            high_frequency_count = 0
            funding_arbitrage_volume = 0.0
            funding_arbitrage_count = 0
            
            if results:
                for row in results:
                    detection_type = row['detection_type']
                    count = row['count']
                    volume = row['volume']
                    avg_score = row['avg_score']

                    if detection_type == 'wash_trading':
                        wash_trading_volume = float(volume or 0)
                        wash_trading_count = int(count or 0)
                    elif detection_type == 'high_frequency':
                        high_frequency_volume = float(volume or 0)
                        high_frequency_count = int(count or 0)
                    elif detection_type == 'funding_arbitrage':
                        funding_arbitrage_volume = float(volume or 0)
                        funding_arbitrage_count = int(count or 0)
            
            # 计算总异常交易量和比例
            total_abnormal_volume = wash_trading_volume + high_frequency_volume + funding_arbitrage_volume
            total_volume = getattr(basic_metrics, 'total_volume', 0)
            abnormal_ratio = total_abnormal_volume / total_volume if total_volume > 0 else 0
            
            # 计算各类型比例
            wash_trading_ratio = wash_trading_volume / total_volume if total_volume > 0 else 0
            high_frequency_ratio = high_frequency_volume / total_volume if total_volume > 0 else 0
            funding_arbitrage_ratio = funding_arbitrage_volume / total_volume if total_volume > 0 else 0
            
            # 风险事件总数
            risk_events_count = wash_trading_count + high_frequency_count + funding_arbitrage_count
            
            # 计算真实交易规模
            real_trading_scale = total_volume - total_abnormal_volume
            
            logger.debug(f"用户 {user_id} 异常分析结果: 总异常量={total_abnormal_volume:.2f}, 风险事件={risk_events_count}")
            
            return AbnormalAnalysis(
                abnormal_volume=total_abnormal_volume,
                abnormal_ratio=abnormal_ratio,
                wash_trading_volume=wash_trading_volume,
                high_frequency_volume=high_frequency_volume,
                funding_arbitrage_volume=funding_arbitrage_volume,
                risk_events_count=risk_events_count,
                abnormal_details={
                    'wash_trading_count': wash_trading_count,
                    'wash_trading_ratio': wash_trading_ratio,
                    'high_frequency_count': high_frequency_count,
                    'high_frequency_ratio': high_frequency_ratio,
                    'funding_arbitrage_count': funding_arbitrage_count,
                    'funding_arbitrage_ratio': funding_arbitrage_ratio,
                    'real_trading_scale': real_trading_scale,
                    'total_abnormal_volume': total_abnormal_volume,
                    'total_abnormal_ratio': abnormal_ratio
                }
            )
            
        except Exception as e:
            logger.error(f"计算用户 {user_id} 异常分析失败: {str(e)}")
            return AbnormalAnalysis()
    
    def _create_empty_result(self, user_id: str, message: str, positions: List[PositionData] = None) -> UserBehaviorProfile:
        """
        创建空的分析结果 - 严格模式：明确标识为无效数据，不返回误导性信息
        """
        now = datetime.now()
        
        # 🚀 创建明确的"数据不足"专业度评分
        empty_scores = ProfessionalScores()
        # trader_type默认值已经是"数据不足"，无需再次设置
        empty_scores.total_score = 0.0
        empty_scores.confidence_level = 0.0
        empty_scores.profitability_score = 0.0
        empty_scores.risk_control_score = 0.0
        empty_scores.trading_behavior_score = 0.0
        empty_scores.market_understanding_score = 0.0
        
        # 🚀 修复: 即使是空结果也要正确设置时间范围
        analysis_period_start = now
        analysis_period_end = now

        if positions:
            analysis_period_start = positions[0].open_time if positions else now
            # 找到最后一个有效的 close_time
            valid_close_times = [pos.close_time for pos in positions if pos.close_time is not None]
            if valid_close_times:
                analysis_period_end = max(valid_close_times)
            else:
                # 如果没有有效的 close_time，使用最后一个 open_time + 15分钟作为估算
                last_open_time = max(pos.open_time for pos in positions)
                analysis_period_end = last_open_time + timedelta(minutes=15)

        result = UserBehaviorProfile(
            user_id=user_id,
            analysis_timestamp=now,
            analysis_period_start=analysis_period_start,
            analysis_period_end=analysis_period_end,
            basic_metrics=BasicMetrics(),
            derived_metrics=DerivedMetrics(),
            professional_scores=empty_scores,
            coin_win_rate_analysis=CoinWinRateAnalysis(),
            # hedge_statistics=HedgeStatistics(),  # 对冲统计已移除
            trading_preferences=TradingPreferences(),
            abnormal_analysis=AbnormalAnalysis(),
            fund_scale_category="数据不足",  # 🚀 明确标识
            data_quality_score=0.0,  # 🚀 明确的低质量分数
            analysis_confidence=0.0   # 🚀 零置信度
        )

        # 🚀 添加错误信息标识 - 用于API层判断
        result._error_message = message
        result._is_empty_result = True
        result._should_return_error = True  # 🚀 新增：标识应该返回错误而不是结果

        # 🚀 即使是空结果也保存positions数据
        if positions:
            result._positions = positions

        return result
    
    def generate_analysis_report(self, analysis_result: UserBehaviorProfile) -> Dict:
        """
        生成分析报告
        
        Args:
            analysis_result: 分析结果
            
        Returns:
            Dict: 格式化的分析报告
        """
        if analysis_result.data_quality_score <= 0:
            return {
                'user_id': analysis_result.user_id,
                'status': 'failed',
                'message': '数据质量不足',
                'analysis_date': analysis_result.analysis_timestamp.isoformat()
            }
        
        # 获取各组件的摘要
        basic_summary = {
            'total_volume': analysis_result.basic_metrics.total_volume,
            'total_trades': analysis_result.basic_metrics.total_trades,
            'win_rate': analysis_result.derived_metrics.win_rate,
            'total_pnl': analysis_result.basic_metrics.total_profit + analysis_result.basic_metrics.total_loss,
            'avg_leverage': analysis_result.derived_metrics.avg_leverage,
            'total_trading_days': analysis_result.basic_metrics.total_trading_days
        }
        
        derived_summary = analysis_result.derived_metrics.__dict__
        coin_summary = {
            'total_analyzed_coins': analysis_result.coin_win_rate_analysis.total_analyzed_coins,
            'avg_coin_win_rate': analysis_result.coin_win_rate_analysis.avg_coin_win_rate,
            'advantage_coins_count': len(analysis_result.coin_win_rate_analysis.advantage_coins)
        }
        
        coin_insights = {
            'top_performer': None,
            'trading_specialty': '正在积累交易经验',
            'improvement_suggestions': []
        }
        
        # hedge_summary = {}  # 对冲摘要已移除
        # hedge_insights = {}
        #     hedge_summary = {
        #         'hedge_positions_count': analysis_result.hedge_statistics.hedge_positions_count,
        #         'max_concurrent_positions': analysis_result.hedge_statistics.max_concurrent_positions
        #     }
        #     hedge_insights = {'hedge_strategy': '基础对冲'}
        
        score_interpretation = {
            'level_description': '根据综合评分确定的交易水平',
            'trader_type': analysis_result.professional_scores.trader_type,
            'percentile_rank': '中等水平'
        }
        
        report = {
            'user_id': analysis_result.user_id,
            'analysis_date': analysis_result.analysis_timestamp.isoformat(),
            'status': 'success',
            
            # 核心评分信息
            'professionalism_score': {
                'total_score': analysis_result.professional_scores.total_score,
                'trader_type': analysis_result.professional_scores.trader_type,
                'fund_scale': analysis_result.fund_scale_category,
                'score_breakdown': {
                    'profitability': analysis_result.professional_scores.profitability_score,
                    'risk_control': analysis_result.professional_scores.risk_control_score,
                    'trading_behavior': analysis_result.professional_scores.trading_behavior_score,
                    'market_understanding': analysis_result.professional_scores.market_understanding_score
                },
                'interpretation': score_interpretation
            },
            
            # 基础交易数据
            'trading_summary': {
                'total_volume': basic_summary['total_volume'],
                'total_trades': basic_summary['total_trades'],
                'win_rate': basic_summary['win_rate'],
                'total_pnl': basic_summary['total_pnl'],
                'avg_leverage': basic_summary['avg_leverage'],
                'trading_days': basic_summary['total_trading_days']
            },
            
            # 币种分析结果
            'coin_analysis': {
                'summary': coin_summary,
                'insights': coin_insights,
                'top_performing_coins': analysis_result.coin_win_rate_analysis.coin_performance_ranking[:5]
            },
            
            # 对冲分析结果（已移除）
            # 'hedge_analysis': {
            #     'summary': hedge_summary,
            #     'insights': hedge_insights
            # } if hedge_summary else None,
            
            # 改进建议
            'improvement_suggestions': [],  # ProfessionalScores中没有improvement_suggestions字段
            
            # 详细指标（可选）
            'detailed_metrics': {
                'basic_metrics': derived_summary,
                'score_details': {}  # ProfessionalScores中没有score_details字段
            }
        }
        
        return report
    
    def batch_analyze_users(self, user_positions_data: Dict[str, List[PositionData]]) -> Dict[str, UserBehaviorProfile]:
        """
        批量分析多个用户
        
        Args:
            user_positions_data: 用户持仓数据字典，key为用户ID，value为持仓列表
            
        Returns:
            Dict: 用户分析结果字典
        """
        logger.info(f"开始批量分析 {len(user_positions_data)} 个用户")
        
        results = {}
        for user_id, positions in user_positions_data.items():
            try:
                result = self.analyze_user_behavior(user_id, positions)
                results[user_id] = result
            except Exception as e:
                logger.error(f"分析用户 {user_id} 时发生错误: {str(e)}")
                results[user_id] = self._create_empty_result(user_id, f"批量分析失败: {str(e)}")
        
        successful_count = sum(1 for result in results.values() if result.data_quality_score > 0)
        logger.info(f"批量分析完成: 成功 {successful_count}/{len(user_positions_data)} 个用户")
        
        return results
    
    def get_analyzer_status(self) -> Dict:
        """获取分析器状态"""
        return {
            'initialized': True,
            'config_loaded': bool(self.config),
            'analyzers': {
                'basic_calculator': bool(self.basic_calculator),
                'derived_calculator': bool(self.derived_calculator),
                'coin_analyzer': bool(self.coin_analyzer),
                # 'hedge_calculator': bool(self.hedge_calculator),  # 对冲计算器已移除
                'professionalism_scorer': bool(self.professionalism_scorer)
            },
            # 'hedge_enabled': self.config.get('hedge_display', {}).get('enabled', False),  # 对冲功能已移除
            'thresholds': self.config.get('thresholds', {}),
            'version': '1.0.0'
        }
    
    def update_config(self, new_config: Dict) -> bool:
        """
        更新配置
        
        Args:
            new_config: 新的配置字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 验证配置格式
            required_keys = ['thresholds', 'scoring_weights', 'trader_classification']
            for key in required_keys:
                if key not in new_config:
                    logger.error(f"配置缺少必需的键: {key}")
                    return False
            
            # 更新配置
            self.config.update(new_config)
            
            # 重新初始化分析器
            self.basic_calculator = BasicMetricsCalculator(self.config)
            self.derived_calculator = DerivedMetricsCalculator(self.config)
            self.coin_analyzer = CoinWinRateAnalyzer(self.config)
            # self.hedge_calculator = HedgeDisplayCalculator(self.config)  # 对冲计算器已移除
            self.professionalism_scorer = ProfessionalismScorer(self.config)
            
            logger.info("配置更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新配置时发生错误: {str(e)}")
            return False 