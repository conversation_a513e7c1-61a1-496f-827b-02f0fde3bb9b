#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合约数据适配器
负责新旧存储结构的切换和数据路由
"""

import logging
from typing import Optional, Dict, Any

import sys
import os

# 添加相对路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(backend_dir)

from database.duckdb_manager import DuckDBManager
from database.algorithm_storage_manager import AlgorithmStorageManager
from database.repositories.contract_risk_repository import ContractRiskRepository

logger = logging.getLogger(__name__)

class ContractDataAdapter:
    """合约数据适配器 - 管理新旧存储的切换"""
    
    def __init__(self, db_manager: Optional[DuckDBManager] = None):
        self.db_manager = db_manager or DuckDBManager()
        self.storage_manager = AlgorithmStorageManager(self.db_manager)
        self.repository = ContractRiskRepository()
        # ========== 修改：默认启用新存储 ==========
        self._new_storage_enabled = True  # 强制启用新存储
        # 检查并初始化新存储
        if not self._check_new_storage_available():
            logger.warning("新存储表不存在，正在初始化...")
            self.storage_manager.initialize_tables()
        logger.info("✅ 数据适配器已启用新存储模式")
        
    def _check_new_storage_available(self) -> bool:
        """检查新存储是否可用"""
        return self.storage_manager._check_new_storage_exists()
    
    def enable_new_storage(self) -> bool:
        """启用新存储结构"""
        try:
            if not self._check_new_storage_available():
                logger.warning("新存储结构不可用，请先初始化")
                return False
                
            self._new_storage_enabled = True
            logger.info("新存储结构已启用")
            return True
            
        except Exception as e:
            logger.error(f"启用新存储失败: {e}")
            return False
    
    def disable_new_storage(self) -> bool:
        """禁用新存储结构，回退到旧存储"""
        try:
            self._new_storage_enabled = False
            logger.info("已回退到旧存储结构")
            return True
            
        except Exception as e:
            logger.error(f"禁用新存储失败: {e}")
            return False
    
    def is_new_storage_enabled(self) -> bool:
        """检查新存储是否已启用"""
        return self._new_storage_enabled and self._check_new_storage_available()
    
    def save_analysis_result(self, task_id: str, analysis_type: str, filename: str,
                           total_contracts: int, risk_contracts: int,
                           wash_trading_count: int, cross_bd_count: int,
                           result_data: Dict[str, Any]) -> bool:
        """保存分析结果 - 新存储优先策略"""
        try:
            new_storage_success = False
            legacy_storage_success = False

            # 🚀 修复：优先保存到新存储
            if self.is_new_storage_enabled():
                try:
                    new_result_id = self.storage_manager.store_algorithm_result(
                        task_id=task_id,
                        algorithm_type=self._map_analysis_type(analysis_type),
                        result_data=result_data
                    )

                    if new_result_id:
                        new_storage_success = True
                        logger.info(f"✅ 数据成功保存到新存储，result_id: {new_result_id}")
                    else:
                        logger.warning("⚠️ 保存到新存储失败")

                except Exception as e:
                    logger.error(f"❌ 新存储保存异常: {e}")

            # 🔄 同时保存到老存储作为备份（但不影响主要结果）
            try:
                legacy_storage_success = self.repository.save_analysis_result(
                    task_id, analysis_type, filename, total_contracts,
                    risk_contracts, wash_trading_count, cross_bd_count, result_data
                )

                if legacy_storage_success:
                    logger.info("✅ 数据同时备份到老存储")
                else:
                    logger.warning("⚠️ 老存储备份失败")

            except Exception as e:
                logger.error(f"❌ 老存储备份异常: {e}")

            # 🎯 关键修复：只要新存储成功就认为保存成功
            if new_storage_success:
                logger.info(f"📊 数据保存成功 - 新存储: ✅, 老存储备份: {'✅' if legacy_storage_success else '❌'}")
                return True
            elif legacy_storage_success:
                # 如果新存储失败但老存储成功，也认为成功（向后兼容）
                logger.warning(f"⚠️ 回退到老存储保存 - 新存储: ❌, 老存储: ✅")
                return True
            else:
                # 两个存储都失败
                logger.error(f"❌ 数据保存完全失败 - 新存储: ❌, 老存储: ❌")
                return False

        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            return False
    
    def get_analysis_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取分析结果 - 新存储优先，无回退机制"""
        try:
            # 🚀 移除回退机制：强制使用新存储
            if self.is_new_storage_enabled():
                new_result = self.storage_manager.query_algorithm_results(task_id)
                # 使用修复后的验证逻辑
                if self._validate_new_storage_result(new_result):
                    logger.info(f"✅ 从新存储获取数据: {task_id}")
                    return self._format_for_compatibility(task_id, new_result)
                else:
                    logger.warning(f"⚠️ 新存储数据验证失败: {task_id}")
                    return None

            # 🔄 如果新存储未启用，仍然支持老存储（向后兼容）
            logger.info(f"🔄 新存储未启用，使用兼容性存储: {task_id}")
            legacy_result = self.repository.get_analysis_result(task_id)
            if legacy_result:
                logger.info(f"✅ 从兼容性存储获取数据: {task_id}")
            else:
                logger.warning(f"⚠️ 兼容性存储中未找到数据: {task_id}")

            return legacy_result

        except Exception as e:
            logger.error(f"❌ 获取分析结果失败: {e}")
            return None
    
    def _map_analysis_type(self, analysis_type: str) -> str:
        """映射分析类型到新存储的算法类型"""
        type_mapping = {
            'full': 'suspected_wash_trading',
            'wash_trading': 'suspected_wash_trading',
            'high_frequency': 'high_frequency_trading',
            'brush_trading': 'regular_brush_trading',
            'funding_arbitrage': 'funding_rate_arbitrage'
        }
        return type_mapping.get(analysis_type, 'suspected_wash_trading')
    
    def _validate_new_storage_result(self, result: Dict[str, Any]) -> bool:
        """验证新存储结果的完整性"""
        if not result:
            return False

        # 🔧 修复：只要有基本的结果结构就认为完整，不管是否有风险记录
        # 检查必要的结构字段存在
        if 'contract_risks' not in result:
            return False

        # 检查contract_risks是否为列表类型（可以为空列表）
        contract_risks = result.get('contract_risks', [])
        if not isinstance(contract_risks, list):
            return False

        # ✅ 关键修复：空的contract_risks列表也是有效的结果
        # 这表示算法正常执行但没有检测到风险，这是完全正常的情况

        # 可选：检查是否有summary信息（但不强制要求）
        summary = result.get('summary', {})
        if summary and not isinstance(summary, dict):
            return False

        # 如果结构正确，就认为数据完整
        return True

    # ========== 统计功能迁移到新存储 ==========

    def get_all_analysis_results(self, limit=100, offset=0):
        """获取所有合约风险分析结果 - 新存储优先，无回退机制"""
        try:
            # 🚀 移除回退机制：强制使用新存储
            if self.is_new_storage_enabled():
                logger.info("✅ 使用新存储获取所有分析结果")
                return self.storage_manager.get_all_analysis_results(limit, offset)

            # 🔄 如果新存储未启用，使用兼容性存储（向后兼容）
            logger.info("🔄 新存储未启用，使用兼容性存储获取所有分析结果")
            return self.repository.get_all_analysis_results(limit, offset)

        except Exception as e:
            logger.error(f"❌ 获取所有分析结果失败: {e}")
            return []

    def get_statistics(self):
        """获取合约风险分析统计信息 - 新存储优先，无回退机制"""
        try:
            # 🚀 移除回退机制：强制使用新存储
            if self.is_new_storage_enabled():
                logger.info("✅ 使用新存储获取统计信息")
                return self.storage_manager.get_statistics()

            # 🔄 如果新存储未启用，使用兼容性存储（向后兼容）
            logger.info("🔄 新存储未启用，使用兼容性存储获取统计信息")
            return self.repository.get_statistics()

        except Exception as e:
            logger.error(f"❌ 获取统计信息失败: {e}")
            return {}

    def get_completed_tasks(self):
        """获取已完成的合约分析任务列表 - 新存储优先，无回退机制"""
        try:
            # 🚀 移除回退机制：强制使用新存储
            if self.is_new_storage_enabled():
                logger.info("✅ 使用新存储获取已完成任务")
                return self.storage_manager.get_completed_tasks()

            # 🔄 如果新存储未启用，使用兼容性存储（向后兼容）
            logger.info("🔄 新存储未启用，使用兼容性存储获取已完成任务")
            return self.repository.get_completed_tasks()

        except Exception as e:
            logger.error(f"❌ 获取已完成任务失败: {e}")
            return []

    def _format_for_compatibility(self, task_id: str, new_result: Dict) -> Dict[str, Any]:
        """将新存储结果格式化为兼容的旧格式"""
        # 计算统计信息
        contract_risks = new_result.get('contract_risks', [])
        wash_trading_count = len([r for r in contract_risks 
                                if 'wash_trading' in r.get('detection_type', '')])
        cross_bd_count = len([r for r in contract_risks 
                            if 'cross_bd' in r.get('detection_method', '')])
        
        return {
            'task_id': task_id,
            'analysis_type': 'full',
            'result_data': new_result,
            'total_contracts': new_result.get('total_analyzed', len(contract_risks)),
            'risk_contracts': new_result.get('risks_found', len(contract_risks)),
            'wash_trading_count': wash_trading_count,
            'cross_bd_count': cross_bd_count,
            'created_at': new_result.get('created_at'),
            'updated_at': new_result.get('updated_at')
        }
    
    def get_storage_status(self) -> Dict[str, Any]:
        """获取存储状态信息"""
        return {
            'new_storage_available': self._check_new_storage_available(),
            'new_storage_enabled': self._new_storage_enabled,
            'current_mode': 'new_storage' if self.is_new_storage_enabled() else 'legacy_storage'
        } 