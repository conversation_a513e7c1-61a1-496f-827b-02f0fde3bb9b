"""
数据格式转换器
负责将CompletePosition对象转换为position_analysis表格式
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from decimal import Decimal

# 配置日志记录
logger = logging.getLogger(__name__)

class DataFormatConverter:
    """
    数据格式转换器
    
    负责将CompletePosition对象转换为数据库表格式，
    处理字段映射、数据类型转换和验证
    """
    
    def __init__(self):
        """初始化转换器"""
        # 字段映射配置
        self.field_mapping = self._init_field_mapping()
        
        # 数据类型转换器
        self.type_converters = self._init_type_converters()
        
        logger.info("数据格式转换器初始化完成")
    
    def _init_field_mapping(self) -> Dict[str, str]:
        """
        初始化字段映射配置
        
        返回:
            字段映射字典 {CompletePosition字段: position_analysis字段}
        """
        return {
            # 基础标识字段
            'position_id': 'position_id',
            'member_id': 'member_id', 
            'contract_name': 'contract_name',
            'primary_side': 'primary_side',
            
            # 时间字段
            'first_open_time': 'open_time',
            'first_close_time': 'close_time',
            'total_duration_minutes': 'duration_minutes',
            
            # 金额和价格字段
            'total_open_amount': 'total_open_amount',
            'total_close_amount': 'total_close_amount',
            'avg_open_price': 'avg_open_price',
            'avg_close_price': 'avg_close_price',
            'real_profit': 'total_pnl',
            'total_fee': 'total_commission',
            'leverage': 'leverage',
            
            # 订单类型统计
            'market_orders_open': 'market_orders_open',
            'limit_orders_open': 'limit_orders_open',
            'market_orders_close': 'market_orders_close',
            'limit_orders_close': 'limit_orders_close',
            
            # 仓位模式
            'cross_margin_positions': 'cross_margin_positions',
            'isolated_margin_positions': 'isolated_margin_positions'
        }
    
    def _init_type_converters(self) -> Dict[str, callable]:
        """
        初始化数据类型转换器
        
        返回:
            类型转换器字典
        """
        return {
            'decimal': lambda x: float(x) if x is not None else 0.0,
            'integer': lambda x: int(x) if x is not None else 0,
            'string': lambda x: str(x) if x is not None else '',
            'datetime': lambda x: x if isinstance(x, datetime) else datetime.now(),
            'boolean': lambda x: bool(x) if x is not None else False,
            'json': lambda x: json.dumps(x, ensure_ascii=False, default=str) if x is not None else '{}'
        }
    
    def convert_complete_position_to_db_record(self, position, task_id: str, processing_mode: str = 'incremental') -> Dict[str, Any]:
        """
        将CompletePosition对象转换为数据库记录格式
        
        参数:
            position: CompletePosition对象
            task_id: 任务ID
            processing_mode: 处理模式
            
        返回:
            数据库记录字典
        """
        try:
            # 基础字段转换
            record = self._convert_basic_fields(position)
            
            # 计算衍生字段
            record.update(self._calculate_derived_fields(position))
            
            # 添加元数据字段
            record.update(self._add_metadata_fields(task_id, processing_mode))
            
            # 添加增量处理数据
            record.update(self._convert_incremental_data(position, processing_mode))
            
            # 数据验证和清理
            record = self._validate_and_clean_record(record)
            
            logger.debug(f"成功转换订单 {position.position_id} 的数据格式")
            return record
            
        except Exception as e:
            logger.error(f"转换订单 {getattr(position, 'position_id', 'UNKNOWN')} 数据格式失败: {str(e)}")
            raise
    
    def _convert_basic_fields(self, position) -> Dict[str, Any]:
        """转换基础字段"""
        record = {}
        
        for pos_field, db_field in self.field_mapping.items():
            try:
                value = getattr(position, pos_field, None)
                record[db_field] = value
            except AttributeError:
                logger.warning(f"CompletePosition缺少字段: {pos_field}")
                record[db_field] = None
        
        return record
    
    def _calculate_derived_fields(self, position) -> Dict[str, Any]:
        """计算衍生字段 - 🔧 修复：确保所有数值都转换为float类型以避免Decimal运算错误"""
        derived = {}

        try:
            # 🔧 修复：安全获取数值字段并转换为float类型
            def safe_float(value):
                """安全转换为float类型，处理Decimal、None等情况"""
                if value is None:
                    return 0.0
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return 0.0

            # 计算净盈亏 - 确保所有数值都是float类型
            total_pnl = safe_float(getattr(position, 'real_profit', 0.0))
            total_commission = safe_float(getattr(position, 'total_fee', 0.0))
            derived['net_pnl'] = total_pnl - total_commission

            # 计算开仓和平仓交易数量
            derived['open_trades_count'] = getattr(position, 'open_trades_count', 0)
            derived['close_trades_count'] = getattr(position, 'close_trades_count', 0)

            # 计算总交易数量
            derived['total_trades'] = derived['open_trades_count'] + derived['close_trades_count']

            # 计算平均持仓时长 - 确保是float类型
            duration = safe_float(getattr(position, 'total_duration_minutes', 0.0))
            derived['avg_holding_time'] = duration

            # 计算盈亏率 - 确保所有数值都是float类型
            open_amount = safe_float(getattr(position, 'total_open_amount', 0.0))
            if open_amount > 0:
                derived['pnl_ratio'] = (total_pnl / open_amount) * 100
            else:
                derived['pnl_ratio'] = 0.0

            # 计算手续费率 - 确保所有数值都是float类型
            if open_amount > 0:
                derived['commission_ratio'] = (total_commission / open_amount) * 100
            else:
                derived['commission_ratio'] = 0.0
                
        except Exception as e:
            logger.warning(f"计算衍生字段失败: {str(e)}")
            # 设置默认值
            derived.update({
                'net_pnl': 0.0,
                'open_trades_count': 0,
                'close_trades_count': 0,
                'total_trades': 0,
                'avg_holding_time': 0.0,
                'pnl_ratio': 0.0,
                'commission_ratio': 0.0
            })
        
        return derived
    
    def _add_metadata_fields(self, task_id: str, processing_mode: str) -> Dict[str, Any]:
        """添加元数据字段"""
        now = datetime.now()
        
        return {
            'task_id': task_id,
            'processing_mode': processing_mode,
            'created_at': now,
            'updated_at': now,
            'data_version': '3.0',  # 增量处理版本
            'source': 'incremental_analyzer'
        }
    
    def _convert_incremental_data(self, position, processing_mode: str) -> Dict[str, Any]:
        """
        转换增量处理相关数据
        
        注意：由于表结构限制，暂时将增量数据序列化为JSON存储
        """
        incremental_data = {}
        
        try:
            # 增量处理核心数据
            incremental_info = {
                'current_volume': getattr(position, 'current_volume', 0.0),
                'is_position_complete': getattr(position, 'is_position_complete', True),
                'needs_completion': getattr(position, 'needs_completion', False),
                'missing_volume': getattr(position, 'missing_volume', 0.0),
                
                # 交易序列数据
                'transaction_intervals': getattr(position, 'transaction_intervals', []),
                'transaction_directions': getattr(position, 'transaction_directions', []),
                'total_transactions': getattr(position, 'total_transactions', 0),
                'same_minute_transactions': getattr(position, 'same_minute_transactions', 0),
                
                # 时间分析数据
                'first_transaction_time': getattr(position, 'first_transaction_time', None),
                'last_transaction_time': getattr(position, 'last_transaction_time', None),
                
                # 处理模式
                'processing_mode': processing_mode,
                'conversion_timestamp': datetime.now().isoformat()
            }
            
            # 序列化为JSON（临时方案）
            incremental_data['incremental_data'] = self.type_converters['json'](incremental_info)
            
        except Exception as e:
            logger.warning(f"转换增量数据失败: {str(e)}")
            incremental_data['incremental_data'] = '{}'
        
        return incremental_data
    
    def _validate_and_clean_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理记录数据"""
        cleaned_record = {}
        
        for field, value in record.items():
            try:
                # 处理None值
                if value is None:
                    cleaned_record[field] = self._get_default_value(field)
                    continue
                
                # 数据类型转换和验证
                cleaned_value = self._convert_field_type(field, value)
                cleaned_record[field] = cleaned_value
                
            except Exception as e:
                logger.warning(f"清理字段 {field} 失败: {str(e)}")
                cleaned_record[field] = self._get_default_value(field)
        
        # 验证必要字段
        self._validate_required_fields(cleaned_record)
        
        return cleaned_record
    
    def _get_default_value(self, field: str) -> Any:
        """获取字段的默认值"""
        # 字符串字段
        if field in ['position_id', 'member_id', 'contract_name', 'task_id', 'processing_mode', 'data_version', 'source']:
            return ''
        
        # 时间字段
        if field in ['open_time', 'close_time', 'created_at', 'updated_at']:
            return datetime.now()
        
        # 数值字段
        if field in ['primary_side', 'open_trades_count', 'close_trades_count', 'total_trades',
                    'market_orders_open', 'limit_orders_open', 'market_orders_close', 'limit_orders_close',
                    'cross_margin_positions', 'isolated_margin_positions', 'total_transactions', 'same_minute_transactions']:
            return 0
        
        # 浮点数字段
        if field in ['duration_minutes', 'total_open_amount', 'total_close_amount', 'avg_open_price', 'avg_close_price',
                    'total_pnl', 'total_commission', 'net_pnl', 'leverage', 'avg_holding_time', 'pnl_ratio', 'commission_ratio',
                    'current_volume', 'missing_volume']:
            return 0.0
        
        # 布尔字段
        if field in ['is_position_complete', 'needs_completion']:
            return False
        
        # JSON字段
        if field in ['incremental_data']:
            return '{}'
        
        # 默认返回None
        return None
    
    def _convert_field_type(self, field: str, value: Any) -> Any:
        """转换字段类型"""
        # 根据字段名称确定类型转换
        if field in ['duration_minutes', 'total_open_amount', 'total_close_amount', 'avg_open_price', 'avg_close_price',
                    'total_pnl', 'total_commission', 'net_pnl', 'leverage', 'avg_holding_time', 'pnl_ratio', 'commission_ratio',
                    'current_volume', 'missing_volume']:
            return self.type_converters['decimal'](value)
        
        if field in ['primary_side', 'open_trades_count', 'close_trades_count', 'total_trades',
                    'market_orders_open', 'limit_orders_open', 'market_orders_close', 'limit_orders_close',
                    'cross_margin_positions', 'isolated_margin_positions', 'total_transactions', 'same_minute_transactions']:
            return self.type_converters['integer'](value)
        
        if field in ['position_id', 'member_id', 'contract_name', 'task_id', 'processing_mode', 'data_version', 'source']:
            return self.type_converters['string'](value)
        
        if field in ['open_time', 'close_time', 'created_at', 'updated_at']:
            return self.type_converters['datetime'](value)
        
        if field in ['is_position_complete', 'needs_completion']:
            return self.type_converters['boolean'](value)
        
        if field in ['incremental_data']:
            return self.type_converters['json'](value)
        
        # 默认返回原值
        return value
    
    def _validate_required_fields(self, record: Dict[str, Any]) -> None:
        """验证必要字段"""
        required_fields = ['position_id', 'member_id', 'contract_name']
        
        for field in required_fields:
            if not record.get(field):
                raise ValueError(f"必要字段 {field} 不能为空")
    
    def batch_convert_positions(self, positions: Dict, task_id: str, processing_mode: str = 'incremental') -> List[Dict[str, Any]]:
        """
        批量转换CompletePosition对象
        
        参数:
            positions: CompletePosition对象字典
            task_id: 任务ID
            processing_mode: 处理模式
            
        返回:
            数据库记录列表
        """
        records = []
        success_count = 0
        error_count = 0
        
        for position_id, position in positions.items():
            try:
                record = self.convert_complete_position_to_db_record(position, task_id, processing_mode)
                records.append(record)
                success_count += 1
                
            except Exception as e:
                logger.error(f"批量转换订单 {position_id} 失败: {str(e)}")
                error_count += 1
                continue
        
        logger.info(f"批量转换完成: 成功 {success_count}, 失败 {error_count}")
        return records
