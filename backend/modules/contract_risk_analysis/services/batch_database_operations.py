"""
批量数据库操作类
提供高效的批量查询、插入、更新操作，优化数据库性能
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import time

# 配置日志记录
logger = logging.getLogger(__name__)

class BatchDatabaseOperations:
    """
    批量数据库操作类
    
    提供高效的批量数据库操作，包括：
    1. 批量插入和更新
    2. 批量查询和条件查询
    3. 事务管理
    4. 性能监控和优化
    """
    
    def __init__(self, db_manager=None, batch_size: int = 1000):
        """
        初始化批量数据库操作类
        
        参数:
            db_manager: 数据库管理器
            batch_size: 批量操作大小
        """
        self.db_manager = db_manager
        if not self.db_manager:
            from database.duckdb_manager import DuckDBManager
            self.db_manager = DuckDBManager()
        
        self.batch_size = batch_size
        
        # 性能统计
        self.stats = {
            'total_operations': 0,
            'total_records': 0,
            'total_time': 0.0,
            'batch_operations': 0,
            'failed_operations': 0
        }
        
        logger.info(f"批量数据库操作类初始化完成，批量大小: {batch_size}")
    
    def batch_insert_positions(self, records: List[Dict[str, Any]], table_name: str = 'position_analysis') -> Dict[str, Any]:
        """
        批量插入订单数据
        
        参数:
            records: 记录列表
            table_name: 目标表名
            
        返回:
            操作结果
        """
        if not records:
            return {'success': True, 'inserted_count': 0, 'message': '无数据需要插入'}
        
        start_time = time.time()
        total_inserted = 0
        failed_count = 0
        
        logger.info(f"开始批量插入 {len(records)} 条记录到 {table_name}")
        
        try:
            # 获取表结构
            table_columns = self._get_table_columns(table_name)
            
            # 分批处理
            for batch_start in range(0, len(records), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(records))
                batch_records = records[batch_start:batch_end]
                
                try:
                    inserted = self._insert_batch(batch_records, table_name, table_columns)
                    total_inserted += inserted
                    
                    logger.debug(f"批次 {batch_start//self.batch_size + 1}: 插入 {inserted} 条记录")
                    
                except Exception as e:
                    logger.error(f"批次插入失败 (记录 {batch_start}-{batch_end}): {str(e)}")
                    failed_count += len(batch_records)
                    continue
            
            # 更新统计
            processing_time = time.time() - start_time
            self._update_stats('insert', len(records), processing_time, failed_count)
            
            result = {
                'success': True,
                'inserted_count': total_inserted,
                'failed_count': failed_count,
                'processing_time': processing_time,
                'message': f'批量插入完成: 成功 {total_inserted}, 失败 {failed_count}'
            }
            
            logger.info(f"批量插入完成: {result['message']}, 耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"批量插入失败: {str(e)}"
            logger.error(error_msg)
            
            return {
                'success': False,
                'inserted_count': total_inserted,
                'failed_count': len(records) - total_inserted,
                'processing_time': processing_time,
                'error': error_msg
            }
    
    def batch_upsert_positions(self, records: List[Dict[str, Any]], table_name: str = 'position_analysis') -> Dict[str, Any]:
        """
        批量UPSERT操作（插入或更新）

        参数:
            records: 记录列表
            table_name: 目标表名

        返回:
            操作结果
        """
        if not records:
            return {'success': True, 'upserted_count': 0, 'message': '无数据需要处理'}

        start_time = time.time()
        total_upserted = 0
        failed_count = 0

        logger.info(f"开始批量UPSERT {len(records)} 条记录到 {table_name}")

        try:
            # 获取表结构
            table_columns = self._get_table_columns(table_name)

            # 分批处理记录
            for batch_start in range(0, len(records), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(records))
                batch_records = records[batch_start:batch_end]

                try:
                    upserted = self._upsert_batch(batch_records, table_name, table_columns)
                    total_upserted += upserted

                    logger.debug(f"批次 {batch_start//self.batch_size + 1}: UPSERT {upserted} 条记录")

                except Exception as e:
                    logger.error(f"批次UPSERT失败 (记录 {batch_start}-{batch_end}): {str(e)}")
                    failed_count += len(batch_records)
                    continue
            
            # 更新统计
            processing_time = time.time() - start_time
            self._update_stats('upsert', len(records), processing_time, failed_count)

            result = {
                'success': True,
                'upserted_count': total_upserted,
                'failed_count': failed_count,
                'processing_time': processing_time,
                'message': f'批量UPSERT完成: 成功 {total_upserted}, 失败 {failed_count}'
            }

            logger.info(f"批量UPSERT完成: {result['message']}, 耗时: {processing_time:.2f}秒")
            return result

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"批量UPSERT失败: {str(e)}"
            logger.error(error_msg)

            return {
                'success': False,
                'upserted_count': total_upserted,
                'failed_count': len(records) - total_upserted,
                'processing_time': processing_time,
                'error': error_msg
            }
    
    def batch_query_positions(self, position_ids: List[str], table_name: str = 'position_analysis') -> Dict[str, Any]:
        """
        批量查询订单数据
        
        参数:
            position_ids: 订单ID列表
            table_name: 源表名
            
        返回:
            查询结果
        """
        if not position_ids:
            return {'success': True, 'records': [], 'message': '无查询条件'}
        
        start_time = time.time()
        all_records = []
        
        logger.info(f"开始批量查询 {len(position_ids)} 个订单")
        
        try:
            # 分批查询
            for batch_start in range(0, len(position_ids), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(position_ids))
                batch_ids = position_ids[batch_start:batch_end]
                
                try:
                    batch_records = self._query_batch(batch_ids, table_name)
                    all_records.extend(batch_records)
                    
                    logger.debug(f"批次 {batch_start//self.batch_size + 1}: 查询到 {len(batch_records)} 条记录")
                    
                except Exception as e:
                    logger.error(f"批次查询失败 (ID {batch_start}-{batch_end}): {str(e)}")
                    continue
            
            # 更新统计
            processing_time = time.time() - start_time
            self._update_stats('query', len(position_ids), processing_time, 0)
            
            result = {
                'success': True,
                'records': all_records,
                'query_count': len(position_ids),
                'found_count': len(all_records),
                'processing_time': processing_time,
                'message': f'批量查询完成: 查询 {len(position_ids)}, 找到 {len(all_records)}'
            }
            
            logger.info(f"批量查询完成: {result['message']}, 耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"批量查询失败: {str(e)}"
            logger.error(error_msg)
            
            return {
                'success': False,
                'records': all_records,
                'query_count': len(position_ids),
                'found_count': len(all_records),
                'processing_time': processing_time,
                'error': error_msg
            }
    
    def batch_delete_positions(self, position_ids: List[str], table_name: str = 'position_analysis') -> Dict[str, Any]:
        """
        批量删除订单数据
        
        参数:
            position_ids: 订单ID列表
            table_name: 目标表名
            
        返回:
            删除结果
        """
        if not position_ids:
            return {'success': True, 'deleted_count': 0, 'message': '无数据需要删除'}
        
        start_time = time.time()
        total_deleted = 0
        
        logger.info(f"开始批量删除 {len(position_ids)} 个订单")
        
        try:
            # 分批删除
            for batch_start in range(0, len(position_ids), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(position_ids))
                batch_ids = position_ids[batch_start:batch_end]
                
                try:
                    deleted = self._delete_batch(batch_ids, table_name)
                    total_deleted += deleted
                    
                    logger.debug(f"批次 {batch_start//self.batch_size + 1}: 删除 {deleted} 条记录")
                    
                except Exception as e:
                    logger.error(f"批次删除失败 (ID {batch_start}-{batch_end}): {str(e)}")
                    continue
            
            # 更新统计
            processing_time = time.time() - start_time
            self._update_stats('delete', len(position_ids), processing_time, 0)
            
            result = {
                'success': True,
                'deleted_count': total_deleted,
                'processing_time': processing_time,
                'message': f'批量删除完成: 删除 {total_deleted} 条记录'
            }
            
            logger.info(f"批量删除完成: {result['message']}, 耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"批量删除失败: {str(e)}"
            logger.error(error_msg)
            
            return {
                'success': False,
                'deleted_count': total_deleted,
                'processing_time': processing_time,
                'error': error_msg
            }
    
    def _get_table_columns(self, table_name: str) -> List[str]:
        """获取表的列名"""
        try:
            desc = self.db_manager.execute_sql(f'DESCRIBE {table_name}')
            return [col['column_name'] for col in desc]
        except Exception as e:
            logger.error(f"获取表 {table_name} 结构失败: {str(e)}")
            return []
    
    def _insert_batch(self, records: List[Dict[str, Any]], table_name: str, table_columns: List[str]) -> int:
        """执行批量插入"""
        if not records:
            return 0

        # 过滤有效字段
        valid_columns = [col for col in table_columns if col in records[0]]

        # 构建SQL
        placeholders = ', '.join(['?' for _ in valid_columns])
        columns_str = ', '.join(valid_columns)
        sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

        # 准备数据
        batch_data = []
        for record in records:
            row_data = [record.get(col) for col in valid_columns]
            batch_data.append(row_data)

        # 🚀 使用现有的高效批量插入方法
        try:
            self.db_manager.execute_many(sql, batch_data)
            return len(batch_data)
        except Exception as e:
            logger.warning(f"批量插入失败，降级到逐条插入: {str(e)}")
            # 降级到逐条插入
            success_count = 0
            for row_data in batch_data:
                try:
                    self.db_manager.execute_sql(sql, row_data)
                    success_count += 1
                except Exception as single_e:
                    logger.debug(f"插入单条记录失败: {str(single_e)}")
                    continue
            return success_count
    
    def _upsert_batch(self, records: List[Dict[str, Any]], table_name: str, table_columns: List[str]) -> int:
        """执行批量UPSERT"""
        if not records:
            return 0
        
        # 过滤有效字段
        valid_columns = [col for col in table_columns if col in records[0]]
        
        # 构建SQL (使用INSERT OR REPLACE)
        placeholders = ', '.join(['?' for _ in valid_columns])
        columns_str = ', '.join(valid_columns)
        sql = f"INSERT OR REPLACE INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        # 准备数据
        batch_data = []
        for record in records:
            row_data = [record.get(col) for col in valid_columns]
            batch_data.append(row_data)
        
        # 🚀 使用现有的高效批量UPSERT方法
        try:
            self.db_manager.execute_many(sql, batch_data)
            return len(batch_data)
        except Exception as e:
            logger.warning(f"批量UPSERT失败，降级到逐条处理: {str(e)}")
            # 降级到逐条UPSERT
            success_count = 0
            for row_data in batch_data:
                try:
                    self.db_manager.execute_sql(sql, row_data)
                    success_count += 1
                except Exception as single_e:
                    logger.debug(f"UPSERT单条记录失败: {str(single_e)}")
                    continue
            return success_count
    
    def _query_batch(self, position_ids: List[str], table_name: str) -> List[Dict[str, Any]]:
        """执行批量查询"""
        if not position_ids:
            return []
        
        # 构建IN查询
        placeholders = ', '.join(['?' for _ in position_ids])
        sql = f"SELECT * FROM {table_name} WHERE position_id IN ({placeholders})"
        
        try:
            results = self.db_manager.execute_sql(sql, position_ids)
            return results if results else []
        except Exception as e:
            logger.error(f"批量查询失败: {str(e)}")
            return []
    
    def _delete_batch(self, position_ids: List[str], table_name: str) -> int:
        """执行批量删除"""
        if not position_ids:
            return 0
        
        # 构建DELETE语句
        placeholders = ', '.join(['?' for _ in position_ids])
        sql = f"DELETE FROM {table_name} WHERE position_id IN ({placeholders})"
        
        try:
            self.db_manager.execute_sql(sql, position_ids)
            return len(position_ids)  # DuckDB不返回affected rows，假设全部成功
        except Exception as e:
            logger.error(f"批量删除失败: {str(e)}")
            return 0
    
    def _update_stats(self, operation: str, record_count: int, processing_time: float, failed_count: int):
        """更新性能统计"""
        self.stats['total_operations'] += 1
        self.stats['total_records'] += record_count
        self.stats['total_time'] += processing_time
        self.stats['batch_operations'] += 1
        self.stats['failed_operations'] += failed_count
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.stats.copy()
        
        if stats['total_operations'] > 0:
            stats['avg_time_per_operation'] = stats['total_time'] / stats['total_operations']
            stats['avg_records_per_operation'] = stats['total_records'] / stats['total_operations']
        else:
            stats['avg_time_per_operation'] = 0.0
            stats['avg_records_per_operation'] = 0.0
        
        if stats['total_time'] > 0:
            stats['records_per_second'] = stats['total_records'] / stats['total_time']
        else:
            stats['records_per_second'] = 0.0
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_operations': 0,
            'total_records': 0,
            'total_time': 0.0,
            'batch_operations': 0,
            'failed_operations': 0
        }

        logger.info("性能统计信息已重置")
