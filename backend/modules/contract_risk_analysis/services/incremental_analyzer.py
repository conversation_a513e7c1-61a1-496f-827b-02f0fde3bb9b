"""
增量处理分析器
负责处理增量数据的分析和处理，支持增量/重跑模式
"""

import pandas as pd
import time
from typing import Dict, List, Any, Tuple
import logging

# 配置日志记录
logger = logging.getLogger(__name__)

class IncrementalAnalyzer:
    """增量处理分析器 - 核心流程控制"""
    
    def __init__(self, task_id: str = None):
        """初始化增量分析器"""
        if task_id is None:
            from .task_id_generator import TaskIdGenerator
            # 🔧 修复：生成任务ID时使用数据库实际数据范围
            from database.duckdb_manager import DuckDBManager
            db_manager = DuckDBManager()
            actual_range = TaskIdGenerator.get_database_actual_range(db_manager)
            self.task_id = TaskIdGenerator.generate_task_id(
                processing_mode='incremental',
                actual_data_range=actual_range,
                db_manager=db_manager
            )
        else:
            self.task_id = task_id
        
        # 初始化组件
        self.position_optimizer = None
        self.completion_handler = None

        # 初始化数据库管理器
        from database.duckdb_manager import DuckDBManager
        self.db_manager = DuckDBManager()
        
        # 统计信息
        self.stats = {
            'positions_processed': 0,
            'completions_replaced': 0,
            'new_completions': 0,
            'detection_results': {}
        }
        
        logger.info(f"增量分析器初始化完成，任务ID: {self.task_id}")
    
    def process_incremental_data(self, df: pd.DataFrame, processing_mode: str) -> Dict[str, Any]:
        """
        增量数据处理主流程

        参数:
            df: 输入的交易数据DataFrame
            processing_mode: 处理模式 ('incremental' | 'rerun')

        返回:
            处理结果字典
        """
        if df.empty:
            logger.warning("输入数据为空，跳过增量分析")
            return self._build_empty_result(processing_mode)

        logger.info(f"开始{processing_mode}模式数据处理，数据量: {len(df)}")
        start_time = time.time()

        try:
            # 1. 数据预处理和验证
            df = self._preprocess_data(df)

            # 2. 使用新的序列构建逻辑
            complete_positions = self._build_complete_positions_with_sequence(df, processing_mode)
            self.stats['positions_processed'] = len(complete_positions)

            # 3. 处理补全订单状态（仅在增量/重跑模式下）
            completion_result = {}
            if processing_mode in ['incremental', 'rerun']:
                completion_result = self._handle_incremental_completions(complete_positions, processing_mode)
                self.stats['completions_replaced'] = completion_result.get('replaced_count', 0)
                self.stats['new_completions'] = completion_result.get('new_completions', 0)

            # 🆕 4. 准备检测数据（在现有流程中插入）
            detection_positions = self._prepare_detection_positions(complete_positions, completion_result)
            logger.info(f"✅ 检测数据准备完成，共 {len(detection_positions)} 个订单用于检测")

            # 5. 保存到position_analysis表（使用准备好的检测数据）
            save_success = self._save_complete_positions_to_db(detection_positions, processing_mode)
            logger.info(f"✅ position_analysis表数据保存完成，成功: {save_success}")

            # 🚀 6. 优化：重跑模式直接使用内存中的数据运行算法，避免数据丢失
            if processing_mode == 'rerun':
                logger.info("🚀 重跑模式：直接使用内存中的CompletePosition数据运行算法")
                detection_results = self._run_detection_algorithms_with_memory_data(detection_positions)
            else:
                # 增量模式：使用原有逻辑
                detection_results = self._run_detection_algorithms(detection_positions)

            self.stats['detection_results'] = detection_results

            # 7. 构建返回结果
            processing_time = time.time() - start_time
            result = self._build_success_result(
                processing_mode,
                complete_positions,
                completion_result,
                detection_results,
                processing_time
            )

            logger.info(f"{processing_mode}模式处理完成，耗时: {processing_time:.2f}秒")
            return result

        except Exception as e:
            logger.error(f"{processing_mode}模式处理失败: {str(e)}")
            return self._build_error_result(processing_mode, str(e))
    

    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理和验证 - 增强版本"""
        logger.info("开始数据预处理...")

        if df.empty:
            raise ValueError("输入数据为空")

        original_count = len(df)

        # 基础字段验证
        required_fields = ['position_id', 'member_id', 'contract_name', 'side', 'deal_vol', 'create_time']
        missing_fields = [field for field in required_fields if field not in df.columns]

        if missing_fields:
            raise ValueError(f"缺少必要字段: {missing_fields}")

        # 🔧 增强的position_id处理
        df['position_id'] = df['position_id'].astype(str).str.strip()
        df = df[df['position_id'] != '']  # 移除空的position_id
        df = df[df['position_id'] != 'nan']  # 移除字符串'nan'
        df = df.dropna(subset=['position_id'])  # 移除NaN的position_id

        # 🔧 增强的时间字段处理
        if 'create_time' in df.columns:
            # 先移除明显无效的时间值
            df = df[df['create_time'].notna()]
            df = df[df['create_time'] != '']

            # 尝试多种时间格式
            time_formats = [
                '%Y/%m/%d %H:%M:%S',
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d %H:%M:%S.%f',
                '%Y-%m-%d %H:%M:%S.%f'
            ]

            df['create_time'] = self._parse_datetime_flexible(df['create_time'], time_formats)

            # 移除时间转换失败的记录
            before_time_filter = len(df)
            df = df[df['create_time'].notna()]
            after_time_filter = len(df)

            if after_time_filter < before_time_filter:
                logger.warning(f"移除了 {before_time_filter - after_time_filter} 条时间字段无效的记录")

        # 数据类型转换
        df['deal_vol'] = pd.to_numeric(df['deal_vol'], errors='coerce')
        df['side'] = pd.to_numeric(df['side'], errors='coerce')

        # 移除无效数据
        df = df.dropna(subset=['position_id', 'member_id', 'deal_vol', 'side', 'create_time'])
        df = df[df['deal_vol'] > 0]  # 交易量必须大于0
        df = df[df['side'].isin([1, 2, 3, 4])]  # 有效的side值

        final_count = len(df)
        removed_count = original_count - final_count

        if removed_count > 0:
            logger.warning(f"数据清理：移除了 {removed_count} 条无效记录，剩余 {final_count} 条")

        if df.empty:
            raise ValueError("数据预处理后无有效记录")

        logger.info(f"数据预处理完成，有效数据量: {final_count}")
        return df

    def _parse_datetime_flexible(self, time_series: pd.Series, formats: list) -> pd.Series:
        """灵活的时间解析"""
        result = pd.Series([pd.NaT] * len(time_series), index=time_series.index)

        for fmt in formats:
            try:
                # 尝试当前格式
                mask = result.isna()
                if mask.any():
                    parsed = pd.to_datetime(time_series[mask], format=fmt, errors='coerce')
                    result[mask] = parsed

                    success_count = parsed.notna().sum()
                    if success_count > 0:
                        logger.debug(f"时间格式 '{fmt}' 成功解析 {success_count} 条记录")

            except Exception as e:
                logger.debug(f"时间格式 '{fmt}' 解析失败: {e}")
                continue

        # 最后尝试自动推断格式
        mask = result.isna()
        if mask.any():
            try:
                parsed = pd.to_datetime(time_series[mask], errors='coerce', infer_datetime_format=True)
                result[mask] = parsed

                success_count = parsed.notna().sum()
                if success_count > 0:
                    logger.debug(f"自动推断格式成功解析 {success_count} 条记录")

            except Exception as e:
                logger.debug(f"自动推断时间格式失败: {e}")

        return result



    def _build_complete_positions_with_sequence(self, df: pd.DataFrame, processing_mode: str) -> Dict[str, Any]:
        """构建完整订单 - 使用新的序列构建逻辑"""
        logger.info("开始构建完整订单序列...")
        
        # 延迟导入以避免循环依赖
        from ..optimizers.position_based_optimizer import PositionBasedOptimizer
        
        if not self.position_optimizer:
            self.position_optimizer = PositionBasedOptimizer()
        
        # 调用新的序列构建方法（待实现）
        complete_positions = self.position_optimizer.build_complete_positions_with_sequence(df, processing_mode)
        
        logger.info(f"完整订单构建完成，共 {len(complete_positions)} 个订单")
        return complete_positions
    
    def _handle_incremental_completions(self, complete_positions: Dict, processing_mode: str) -> Dict[str, Any]:
        """处理增量数据中的补全订单状态"""
        logger.info("开始处理补全订单状态...")
        
        # 延迟导入补全状态处理器（待创建）
        try:
            from .completion_status_handler import CompletionStatusHandler
            
            if not self.completion_handler:
                self.completion_handler = CompletionStatusHandler()
            
            result = self.completion_handler.handle_incremental_completions(complete_positions, processing_mode)
            
            logger.info(f"补全订单处理完成，替换: {result.get('replaced_count', 0)}, 新增: {result.get('new_completions', 0)}")
            return result
            
        except ImportError:
            logger.warning("补全状态处理器未实现，跳过补全处理")
            return {'replaced_count': 0, 'new_completions': 0}

    def _prepare_detection_positions(self, original_positions: Dict, completion_result: Dict) -> Dict:
        """
        准备用于检测的订单数据（新增方法，不影响现有流程）
        合并原始完整数据和从completion_status匹配的数据

        参数:
            original_positions: 原始的完整订单数据
            completion_result: 补全处理结果

        返回:
            合并后用于检测的订单数据
        """
        try:
            # 从原始数据开始
            detection_positions = original_positions.copy()

            # 🚀 重跑模式优化：优先使用完整订单数据
            rerun_complete_positions = completion_result.get('complete_positions_for_detection', {})
            if rerun_complete_positions:
                logger.info(f"🚀 重跑模式：使用 {len(rerun_complete_positions)} 个完整订单数据")
                # 重跑模式下，完整订单数据优先级更高
                detection_positions.update(rerun_complete_positions)

            # 添加从completion_status匹配成功的数据（增量模式）
            matched_positions = completion_result.get('matched_for_detection', {})
            if matched_positions:
                logger.info(f"增量模式：合并 {len(matched_positions)} 个从completion_status匹配的订单")
                detection_positions.update(matched_positions)

            # 🚀 重跑模式优化：保持CompletePosition对象的完整性
            # 确保所有必要的字段和交易序列数据都完整保留
            filtered_positions = {}
            for pos_id, position in detection_positions.items():
                # 基本验证
                if hasattr(position, 'member_id') and hasattr(position, 'contract_name'):
                    # 🔍 验证CompletePosition对象的完整性
                    if hasattr(position, 'transactions') and hasattr(position, 'is_completed'):
                        filtered_positions[pos_id] = position
                        # 调试信息：记录数据来源
                        data_source = getattr(position, 'data_source', 'unknown')
                        logger.debug(f"订单 {pos_id} 数据来源: {data_source}, 交易数: {len(position.transactions) if position.transactions else 0}")
                    else:
                        logger.warning(f"订单 {pos_id} CompletePosition对象不完整，但仍保留用于检测")
                        filtered_positions[pos_id] = position
                else:
                    logger.warning(f"订单 {pos_id} 缺少必要字段，跳过检测")

            # 🔍 统计信息：按数据来源分类
            original_count = len([p for p in filtered_positions.values() if getattr(p, 'data_source', 'original') == 'original'])
            merged_count = len([p for p in filtered_positions.values() if getattr(p, 'data_source', 'original') == 'merged_from_completion'])
            db_count = len([p for p in filtered_positions.values() if getattr(p, 'data_source', 'original') == 'from_database'])
            rerun_count = len(rerun_complete_positions) if rerun_complete_positions else 0

            logger.info(f"检测数据准备完成: 原始{original_count}, 合并{merged_count}, 数据库{db_count}, 重跑完整{rerun_count}, 总计{len(filtered_positions)}")
            return filtered_positions

        except Exception as e:
            logger.error(f"准备检测数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            # 出错时返回原始数据，确保流程继续
            return original_positions

    def _run_detection_algorithms(self, complete_positions: Dict) -> Dict[str, Any]:
        """运行检测算法（调用现有的检测算法）"""
        logger.info("开始运行检测算法...")

        detection_results = {
            'wash_trading': [],
            'high_frequency': [],
            'arbitrage': [],
            'total_risks': 0
        }

        try:
            # 🆕 优化：复用已有的PositionBasedOptimizer实例，避免重复初始化
            if complete_positions:
                logger.info(f"开始对敲检测，完整订单数量: {len(complete_positions)}")

                # 复用已有的优化器实例，保持内存中的数据
                if not self.position_optimizer:
                    from ..optimizers.position_based_optimizer import PositionBasedOptimizer
                    self.position_optimizer = PositionBasedOptimizer()

                # 设置完整订单数据
                self.position_optimizer.complete_positions = complete_positions

                # 执行对敲检测
                wash_trading_pairs = self.position_optimizer.optimized_wash_trading_detection()

                if wash_trading_pairs:
                    logger.info(f"检测到 {len(wash_trading_pairs)} 对可疑对敲交易")
                    detection_results['wash_trading'] = wash_trading_pairs
                    detection_results['total_risks'] += len(wash_trading_pairs)
                else:
                    logger.info("未检测到对敲交易风险")


            else:
                logger.warning("没有完整订单数据，跳过检测算法")

        except Exception as e:
            logger.error(f"检测算法执行失败: {str(e)}")
            # 不抛出异常，返回空结果以保证流程继续

        logger.info(f"检测算法运行完成，总风险数: {detection_results['total_risks']}")
        return detection_results

    def _run_detection_algorithms_with_memory_data(self, complete_positions: Dict) -> Dict[str, Any]:
        """
        🚀 重跑模式专用：直接使用内存中的CompletePosition数据运行算法

        优化点：
        1. 避免数据库读写，直接使用内存中的完整数据
        2. 保持CompletePosition对象的完整性，包括交易序列等详细信息
        3. 提高算法执行效率

        参数:
            complete_positions: 内存中的完整订单数据

        返回:
            检测结果字典
        """
        logger.info("🚀 重跑模式：开始使用内存数据运行检测算法...")

        detection_results = {
            'wash_trading': [],
            'high_frequency': [],
            'arbitrage': [],
            'total_risks': 0
        }

        try:
            if complete_positions:
                logger.info(f"🔍 内存数据检测：完整订单数量 {len(complete_positions)}")

                # 🚀 关键优化：直接使用内存中的CompletePosition数据
                # 创建或复用PositionBasedOptimizer实例
                if not self.position_optimizer:
                    from ..optimizers.position_based_optimizer import PositionBasedOptimizer
                    self.position_optimizer = PositionBasedOptimizer()

                # 🚀 核心：直接设置内存中的完整订单数据，避免数据丢失
                self.position_optimizer.complete_positions = complete_positions

                # 🔍 调试信息：验证数据完整性
                total_positions = len(complete_positions)
                completed_positions = sum(1 for pos in complete_positions.values() if hasattr(pos, 'is_completed') and pos.is_completed)
                logger.info(f"📊 内存数据统计: 总订单={total_positions}, 已完成={completed_positions}")

                # 验证关键字段完整性
                sample_position = next(iter(complete_positions.values()))
                has_transactions = hasattr(sample_position, 'transactions') and sample_position.transactions
                logger.info(f"🔍 数据完整性检查: 包含交易序列={has_transactions}")

                # 执行对敲检测算法
                logger.info("🎯 开始执行对敲检测算法...")
                wash_trading_pairs = self.position_optimizer.optimized_wash_trading_detection()

                if wash_trading_pairs:
                    logger.info(f"✅ 检测到 {len(wash_trading_pairs)} 对可疑对敲交易")
                    detection_results['wash_trading'] = wash_trading_pairs
                    detection_results['total_risks'] += len(wash_trading_pairs)
                else:
                    logger.info("ℹ️ 未检测到对敲交易风险")

                # 🆕 可以在这里添加其他算法的调用
                # 例如：高频交易检测、套利检测等
                # high_freq_results = self.position_optimizer.optimized_high_frequency_detection()
                # arbitrage_results = self.position_optimizer.optimized_arbitrage_detection()

            else:
                logger.warning("⚠️ 没有完整订单数据，跳过检测算法")

        except Exception as e:
            logger.error(f"❌ 内存数据检测算法执行失败: {str(e)}")
            import traceback
            traceback.print_exc()
            # 不抛出异常，返回空结果以保证流程继续

        logger.info(f"✅ 内存数据检测算法运行完成，总风险数: {detection_results['total_risks']}")
        return detection_results

    def _convert_detection_results_to_contract_risks(self, detection_results: Dict) -> List[Dict]:
        """将检测结果转换为contract_risks格式"""
        contract_risks = []

        try:
            # 处理对敲交易结果
            wash_trading_pairs = detection_results.get('wash_trading', [])
            for pair in wash_trading_pairs:
                if pair.get('is_wash_trading', False):
                    # 转换为contract_risks格式
                    risk_record = {
                        'detection_type': 'suspected_wash_trading',
                        'detection_method': pair.get('detection_method', 'wash_trading'),
                        'member_id': pair.get('member_id', ''),
                        'user_id': pair.get('user_id', ''),
                        'contract_name': pair.get('contract_name', ''),
                        'opponent_member_id': pair.get('opponent_member_id', ''),
                        'counterparty_ids': pair.get('counterparty_ids', []),
                        'wash_score': pair.get('wash_score', 0),
                        'profit_hedge_score': pair.get('profit_hedge_score', 0),
                        'time_match_score': pair.get('time_match_score', 0),
                        'amount_match_score': pair.get('amount_match_score', 0),
                        'total_profit': pair.get('total_profit', 0),
                        'time_diff_seconds': pair.get('time_diff_seconds', 0),
                        'user_a': pair.get('user_a', {}),
                        'user_b': pair.get('user_b', {}),
                        'severity': self._calculate_severity(pair.get('wash_score', 0)),
                        'reason': self._generate_reason(pair)
                    }
                    contract_risks.append(risk_record)



            logger.info(f"转换检测结果完成，生成 {len(contract_risks)} 条contract_risks记录")
            return contract_risks

        except Exception as e:
            logger.error(f"转换检测结果失败: {str(e)}")
            return []

    def _calculate_severity(self, wash_score: float) -> str:
        """根据评分计算风险严重程度"""
        if wash_score >= 0.9:
            return 'High'
        elif wash_score >= 0.7:
            return 'Medium'
        else:
            return 'Low'

    def _generate_reason(self, pair: Dict) -> str:
        """生成风险原因描述"""
        detection_method = pair.get('detection_method', '')
        wash_score = pair.get('wash_score', 0)

        if 'same_account' in detection_method:
            return f"同账户对敲交易，综合评分: {wash_score:.2f}"
        elif 'cross_account' in detection_method:
            return f"跨账户对敲交易，综合评分: {wash_score:.2f}"
        else:
            return f"疑似对敲交易，综合评分: {wash_score:.2f}"
    
    def _save_complete_positions_to_db(self, complete_positions: Dict, processing_mode: str) -> bool:
        """将CompletePosition数据保存到position_analysis表"""
        logger.info("开始保存数据到position_analysis表...")

        if not complete_positions:
            logger.warning("没有数据需要保存")
            return True

        try:
            # 延迟导入数据库管理器
            from database.duckdb_manager import DuckDBManager
            db_manager = DuckDBManager()

            # 使用专门的数据格式转换器
            from .data_format_converter import DataFormatConverter
            converter = DataFormatConverter()

            # 批量转换数据格式
            position_records = converter.batch_convert_positions(complete_positions, self.task_id, processing_mode)

            # 使用批量数据库操作类
            from .batch_database_operations import BatchDatabaseOperations
            batch_ops = BatchDatabaseOperations(db_manager)

            # 批量保存数据
            if processing_mode == 'rerun':
                # 重跑模式：先删除再插入
                position_ids = [record['position_id'] for record in position_records]
                delete_result = batch_ops.batch_delete_positions(position_ids)
                logger.info(f"重跑模式删除结果: {delete_result['message']}")

                insert_result = batch_ops.batch_insert_positions(position_records)
                success_count = insert_result['inserted_count']
            else:
                # 增量模式：使用UPSERT
                upsert_result = batch_ops.batch_upsert_positions(position_records)
                success_count = upsert_result['upserted_count']

            logger.info(f"position_analysis数据保存完成: 成功保存{success_count}条记录")
            return success_count > 0

        except Exception as e:
            logger.error(f"保存position_analysis数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


    
    def _build_success_result(self, processing_mode: str, complete_positions: Dict,
                            completion_result: Dict, detection_results: Dict,
                            processing_time: float) -> Dict[str, Any]:
        """构建成功结果 - 支持增量数据合并"""
        # 转换检测结果为contract_risks格式
        new_contract_risks = self._convert_detection_results_to_contract_risks(detection_results)

        # 🆕 增量模式：合并历史数据
        logger.info(f"🔍 处理模式: {processing_mode}, 新检测风险数量: {len(new_contract_risks)}")
        if processing_mode == 'incremental':
            logger.info("🔄 开始增量模式数据合并...")
            merged_risks, merged_stats = self._merge_with_historical_data(new_contract_risks, detection_results)
            contract_risks = merged_risks
            total_risks = merged_stats['total_risks']
            wash_trading_count = merged_stats['wash_trading_pairs']
            high_frequency_count = merged_stats['high_frequency_risks']
            arbitrage_count = merged_stats['arbitrage_risks']
            logger.info(f"✅ 增量合并完成: 新增{len(new_contract_risks)}个风险，累积{len(contract_risks)}个风险")
        else:
            # 重跑模式：只使用当前检测结果
            logger.info("🔄 重跑模式，只使用当前检测结果")
            contract_risks = new_contract_risks
            total_risks = detection_results.get('total_risks', 0)
            wash_trading_count = len(detection_results.get('wash_trading', []))
            high_frequency_count = len(detection_results.get('high_frequency', []))
            arbitrage_count = len(detection_results.get('arbitrage', []))

        return {
            'status': 'success',
            'processing_mode': processing_mode,
            'task_id': self.task_id,
            'positions_processed': len(complete_positions),
            'completions_replaced': completion_result.get('replaced_count', 0),
            'new_completions': completion_result.get('new_completions', 0),
            'detection_results': detection_results,
            'complete_positions': complete_positions,  # 添加完整订单数据
            'contract_risks': contract_risks,  # 添加转换后的风险记录（包含历史数据）
            'statistics': {  # 添加统计信息
                'total_positions': len(complete_positions),
                'wash_trading_pairs': wash_trading_count,
                'high_frequency_risks': high_frequency_count,
                'arbitrage_risks': arbitrage_count,
                'total_risks': total_risks,
                'new_risks_found': len(new_contract_risks),  # 新增：本次新发现的风险数量
                'historical_risks_merged': total_risks - len(new_contract_risks) if processing_mode == 'incremental' else 0
            },
            'processing_time': processing_time,
            'message': f'{processing_mode}模式处理完成，本次发现 {len(new_contract_risks)} 个新风险点' +
                      (f'，累积风险总数 {total_risks} 个' if processing_mode == 'incremental' else '')
        }
    
    def _build_error_result(self, processing_mode: str, error_message: str) -> Dict[str, Any]:
        """构建错误结果"""
        return {
            'status': 'error',
            'processing_mode': processing_mode,
            'task_id': self.task_id,
            'error': error_message,
            'positions_processed': 0,
            'completions_replaced': 0,
            'new_completions': 0
        }
    
    def _build_empty_result(self, processing_mode: str) -> Dict[str, Any]:
        """构建空数据结果"""
        return {
            'status': 'success',
            'processing_mode': processing_mode,
            'task_id': self.task_id,
            'positions_processed': 0,
            'completions_replaced': 0,
            'new_completions': 0,
            'message': '输入数据为空，无需处理'
        }
    
    def _merge_with_historical_data(self, new_contract_risks: List[Dict], new_detection_results: Dict) -> Tuple[List[Dict], Dict]:
        """
        合并历史数据与新检测结果

        参数:
            new_contract_risks: 新检测到的风险记录
            new_detection_results: 新检测结果

        返回:
            (合并后的风险记录列表, 合并后的统计信息)
        """
        logger.info(f"📊 开始合并历史数据，新风险数量: {len(new_contract_risks)}")
        try:
            # 1. 查询历史任务数据
            logger.info("🔍 正在查询历史任务数据...")
            historical_risks = self._get_historical_risks()
            logger.info(f"📊 获取到历史风险数据: {len(historical_risks)} 条")

            # 2. 去重合并（基于member_id + contract_name + detection_type）
            logger.info("🔄 正在执行去重合并...")
            merged_risks = self._deduplicate_and_merge_risks(historical_risks, new_contract_risks)
            logger.info(f"🔄 去重合并完成: {len(merged_risks)} 条")

            # 3. 计算合并后的统计信息
            logger.info("📈 正在计算合并后统计信息...")
            merged_stats = self._calculate_merged_statistics(merged_risks)
            logger.info(f"📈 统计信息: {merged_stats}")

            logger.info(f"✅ 数据合并完成: 历史{len(historical_risks)}个 + 新增{len(new_contract_risks)}个 = 合并后{len(merged_risks)}个风险")

            return merged_risks, merged_stats

        except Exception as e:
            logger.error(f"❌ 合并历史数据失败: {e}")
            import traceback
            traceback.print_exc()
            # 如果合并失败，返回新数据
            fallback_stats = {
                'total_risks': len(new_contract_risks),
                'wash_trading_pairs': len(new_detection_results.get('wash_trading', [])),
                'high_frequency_risks': len(new_detection_results.get('high_frequency', [])),
                'arbitrage_risks': len(new_detection_results.get('arbitrage', []))
            }
            logger.warning(f"⚠️  使用回退数据: {fallback_stats}")
            return new_contract_risks, fallback_stats

    def _get_historical_risks(self) -> List[Dict]:
        """获取历史风险数据"""
        try:
            # 延迟导入避免循环依赖
            from .data_adapter import ContractDataAdapter

            adapter = ContractDataAdapter()

            # 查询所有历史任务的风险数据
            # 这里需要实现一个方法来获取所有相关的历史任务
            historical_tasks = self._get_related_historical_tasks()

            all_historical_risks = []
            for task_id in historical_tasks:
                try:
                    task_result = adapter.get_analysis_result(task_id)
                    if task_result and 'result_data' in task_result:
                        result_data = task_result['result_data']
                        if isinstance(result_data, dict) and 'contract_risks' in result_data:
                            task_risks = result_data['contract_risks']
                            if isinstance(task_risks, list):
                                all_historical_risks.extend(task_risks)
                                logger.debug(f"从任务 {task_id} 获取到 {len(task_risks)} 个历史风险")
                except Exception as e:
                    logger.warning(f"获取任务 {task_id} 的历史数据失败: {e}")
                    continue

            logger.info(f"共获取到 {len(all_historical_risks)} 个历史风险记录")
            return all_historical_risks

        except Exception as e:
            logger.error(f"获取历史风险数据失败: {e}")
            return []

    def _get_related_historical_tasks(self) -> List[str]:
        """获取相关的历史任务ID列表"""
        try:
            # 从数据库查询所有已完成的合约风险分析任务
            # 排除当前任务ID
            from database.repositories.task_repository import TaskRepository

            task_repo = TaskRepository()
            # 修正任务类型为 'contract_analysis'
            all_tasks = task_repo.get_tasks_by_type('contract_analysis')
            logger.info(f"查询到 {len(all_tasks)} 个合约分析任务")

            # 过滤出已完成的任务，排除当前任务
            historical_tasks = [
                task['task_id'] for task in all_tasks
                if task['status'] == 'completed' and task['task_id'] != self.task_id
            ]

            logger.info(f"找到 {len(historical_tasks)} 个历史任务用于数据合并")
            if historical_tasks:
                logger.info(f"历史任务ID列表: {historical_tasks[:5]}{'...' if len(historical_tasks) > 5 else ''}")
            return historical_tasks

        except Exception as e:
            logger.error(f"获取历史任务列表失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _deduplicate_and_merge_risks(self, historical_risks: List[Dict], new_risks: List[Dict]) -> List[Dict]:
        """
        去重合并风险数据 - 修复版本（包含对手方信息的去重键）

        参数:
            historical_risks: 历史风险记录
            new_risks: 新风险记录

        返回:
            去重合并后的风险记录列表
        """
        try:
            # 创建去重键的集合，用于快速查找
            # 🚀 修复：去重键包含对手方信息，避免误判重复
            existing_keys = set()
            merged_risks = []

            # 🆕 改进策略：新数据优先，因为新数据可能包含更准确的检测结果
            # 首先添加新数据
            new_added_count = 0
            for risk in new_risks:
                if isinstance(risk, dict):
                    member_id = str(risk.get('member_id', ''))
                    contract_name = str(risk.get('contract_name', ''))
                    detection_type = str(risk.get('detection_type', ''))

                    # 🚀 关键修复：包含对手方信息的去重键
                    counterparty_ids = risk.get('counterparty_ids', [])
                    opponent_id = risk.get('opponent_member_id', '')

                    # 构建对手方标识（优先使用opponent_member_id，其次使用counterparty_ids的第一个）
                    counterparty_key = ''
                    if opponent_id:
                        counterparty_key = str(opponent_id)
                    elif counterparty_ids and len(counterparty_ids) > 0:
                        counterparty_key = str(counterparty_ids[0])

                    dedup_key = f"{member_id}|{contract_name}|{detection_type}|{counterparty_key}"

                    if dedup_key not in existing_keys:
                        existing_keys.add(dedup_key)
                        merged_risks.append(risk)
                        new_added_count += 1
                        logger.debug(f"添加新风险: {member_id} vs {counterparty_key} - {detection_type}")
                    else:
                        logger.debug(f"跳过重复新风险: {member_id} vs {counterparty_key} - {detection_type}")

            # 然后添加历史数据中不重复的部分
            historical_added_count = 0
            for risk in historical_risks:
                if isinstance(risk, dict):
                    member_id = str(risk.get('member_id', ''))
                    contract_name = str(risk.get('contract_name', ''))
                    detection_type = str(risk.get('detection_type', ''))

                    # 🚀 关键修复：包含对手方信息的去重键
                    counterparty_ids = risk.get('counterparty_ids', [])
                    opponent_id = risk.get('opponent_member_id', '')

                    # 构建对手方标识
                    counterparty_key = ''
                    if opponent_id:
                        counterparty_key = str(opponent_id)
                    elif counterparty_ids and len(counterparty_ids) > 0:
                        counterparty_key = str(counterparty_ids[0])

                    dedup_key = f"{member_id}|{contract_name}|{detection_type}|{counterparty_key}"

                    if dedup_key not in existing_keys:
                        existing_keys.add(dedup_key)
                        merged_risks.append(risk)
                        historical_added_count += 1
                        logger.debug(f"添加历史风险: {member_id} vs {counterparty_key} - {detection_type}")
                    else:
                        logger.debug(f"跳过重复历史风险: {member_id} vs {counterparty_key} - {detection_type} (被新数据覆盖)")

            logger.info(f"去重合并完成: 新数据{new_added_count}个 + 历史补充{historical_added_count}个 = 总计{len(merged_risks)}个风险")
            logger.info(f"数据覆盖情况: 历史{len(historical_risks)}个中有{len(historical_risks)-historical_added_count}个被新数据覆盖")
            logger.info(f"🚀 使用改进的去重键（包含对手方信息），避免误判不同交易对为重复")
            return merged_risks

        except Exception as e:
            logger.error(f"去重合并失败: {e}")
            # 如果合并失败，至少返回新数据
            return new_risks

    def _calculate_merged_statistics(self, merged_risks: List[Dict]) -> Dict[str, int]:
        """
        计算合并后的统计信息

        参数:
            merged_risks: 合并后的风险记录列表

        返回:
            统计信息字典
        """
        try:
            stats = {
                'total_risks': len(merged_risks),
                'wash_trading_pairs': 0,
                'high_frequency_risks': 0,
                'arbitrage_risks': 0
            }

            # 统计各类型风险数量
            for risk in merged_risks:
                if isinstance(risk, dict):
                    detection_type = risk.get('detection_type', '').lower()

                    if 'wash_trading' in detection_type or 'wash' in detection_type:
                        stats['wash_trading_pairs'] += 1
                    elif 'high_frequency' in detection_type or 'hft' in detection_type:
                        stats['high_frequency_risks'] += 1
                    elif 'arbitrage' in detection_type or 'funding' in detection_type:
                        stats['arbitrage_risks'] += 1

            logger.info(f"统计信息计算完成: {stats}")
            return stats

        except Exception as e:
            logger.error(f"计算统计信息失败: {e}")
            return {
                'total_risks': len(merged_risks),
                'wash_trading_pairs': 0,
                'high_frequency_risks': 0,
                'arbitrage_risks': 0
            }

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
