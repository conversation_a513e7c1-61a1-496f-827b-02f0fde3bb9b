"""
增强版仓位画像构建器
Enhanced Position Profiler for Wash Trading Detection
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict

logger = logging.getLogger(__name__)

class EnhancedPositionProfiler:
    """增强版仓位画像构建器"""
    
    def __init__(self):
        self.config = {
            'min_position_amount': 10.0,  # 最小仓位金额
            'max_holding_days': 30,  # 最大持仓天数
            'pnl_calculation_method': 'simple',  # 盈亏计算方法
            'risk_free_rate': 0.03  # 无风险利率（年化）
        }
        
        self.position_cache = {}  # 仓位缓存
        self.profile_stats = {
            'total_positions': 0,
            'complete_positions': 0,
            'incomplete_positions': 0,
            'processing_time': 0.0
        }
    
    def build_complete_profiles(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """
        构建完整的仓位画像，包含：
        - 开仓信息（时间、价格、数量、方向）
        - 平仓信息（时间、价格、数量、方向）
        - 持仓周期分析
        - 盈亏计算
        - 风险指标
        """
        start_time = datetime.now()
        profiles = {}
        
        logger.info(f"开始构建仓位画像，数据量: {len(df)} 条")
        
        try:
            # 数据预处理
            df_clean = self._preprocess_data(df)
            
            # 按position_id分组
            if 'position_id' not in df_clean.columns:
                logger.warning("缺少position_id字段，无法构建仓位画像")
                return {}
            
            position_groups = df_clean.groupby('position_id')
            
            for position_id, group in position_groups:
                if pd.isna(position_id):
                    continue
                
                try:
                    profile = self._build_single_position_profile(position_id, group)
                    
                    # 数据完整性检查
                    if self._validate_profile_completeness(profile):
                        profiles[position_id] = profile
                        self.profile_stats['complete_positions'] += 1
                    else:
                        # 记录不完整的仓位
                        profile.update({
                            'is_complete': False,
                            'missing_data': self._identify_missing_data(profile)
                        })
                        profiles[position_id] = profile
                        self.profile_stats['incomplete_positions'] += 1
                    
                    self.profile_stats['total_positions'] += 1
                    
                except Exception as e:
                    logger.error(f"构建仓位 {position_id} 画像失败: {e}")
                    continue
            
            # 记录处理时间
            self.profile_stats['processing_time'] = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"仓位画像构建完成: 总计{self.profile_stats['total_positions']}个, "
                       f"完整{self.profile_stats['complete_positions']}个, "
                       f"不完整{self.profile_stats['incomplete_positions']}个")
            
        except Exception as e:
            logger.error(f"构建仓位画像失败: {e}")
        
        return profiles
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        df_clean = df.copy()
        
        # 时间戳处理 (指定格式避免警告)
        if 'timestamp' in df_clean.columns:
            df_clean['timestamp'] = pd.to_datetime(
                df_clean['timestamp'],
                format='%Y/%m/%d %H:%M:%S',
                errors='coerce'
            )
        
        # 确保必要字段存在
        required_fields = ['member_id', 'position_id', 'side', 'timestamp']
        for field in required_fields:
            if field not in df_clean.columns:
                logger.warning(f"缺少必要字段: {field}")
        
        # 金额字段处理
        if 'deal_vol_usdt' not in df_clean.columns:
            if 'volume' in df_clean.columns and 'price' in df_clean.columns:
                df_clean['deal_vol_usdt'] = df_clean['volume'] * df_clean['price']
            else:
                logger.warning("缺少交易金额信息")
                df_clean['deal_vol_usdt'] = 0.0
        
        # 过滤无效数据
        df_clean = df_clean.dropna(subset=['position_id', 'member_id'])
        
        return df_clean
    
    def _build_single_position_profile(self, position_id: str, group: pd.DataFrame) -> Dict:
        """构建单个仓位的完整画像"""
        profile = {
            'position_id': position_id,
            'member_id': group['member_id'].iloc[0] if len(group) > 0 else '',
            'contract_name': group['contract_name'].iloc[0] if 'contract_name' in group.columns else '',
            'is_complete': False,
            'has_open': False,
            'has_close': False,
            'open_trades': [],
            'close_trades': [],
            'total_open_amount': 0.0,
            'total_close_amount': 0.0,
            'net_position': 0.0,
            'duration_minutes': 0.0,
            'realized_pnl': 0.0,
            'risk_metrics': {},
            'quality_indicators': {}
        }
        
        try:
            # 按交易方向分离
            opens = group[group['side'].isin([1, 3])].copy()  # 1=开多, 3=开空
            closes = group[group['side'].isin([2, 4])].copy() # 2=平空, 4=平多
            
            # 处理开仓数据
            if len(opens) > 0:
                profile['has_open'] = True
                profile['open_trades'] = self._process_trades(opens)
                profile['open_time'] = opens['timestamp'].min()
                profile['open_side'] = opens['side'].iloc[0]
                profile['total_open_amount'] = opens['deal_vol_usdt'].sum()
                profile['avg_open_price'] = self._calculate_weighted_avg_price(opens)
                
            # 处理平仓数据
            if len(closes) > 0:
                profile['has_close'] = True
                profile['close_trades'] = self._process_trades(closes)
                profile['close_time'] = closes['timestamp'].max()
                profile['close_side'] = closes['side'].iloc[0]
                profile['total_close_amount'] = closes['deal_vol_usdt'].sum()
                profile['avg_close_price'] = self._calculate_weighted_avg_price(closes)
            
            # 设置is_closed字段（兼容新检测器）
            profile['is_closed'] = profile['has_close']
            
            # 计算仓位指标
            if profile['has_open'] and profile['has_close']:
                profile['is_complete'] = True
                profile['duration_minutes'] = (
                    profile['close_time'] - profile['open_time']
                ).total_seconds() / 60
                
                # 盈亏计算
                profile['realized_pnl'] = self._calculate_position_pnl(opens, closes)
                
                # 风险指标
                profile['risk_metrics'] = self._calculate_risk_metrics(profile)
                
                # 质量指标
                profile['quality_indicators'] = self._calculate_quality_indicators(profile, group)
            
            # 计算净仓位
            profile['net_position'] = profile['total_open_amount'] - profile['total_close_amount']
            
        except Exception as e:
            logger.error(f"构建仓位 {position_id} 详情失败: {e}")
            profile['error'] = str(e)
        
        return profile
    
    def _process_trades(self, trades_df: pd.DataFrame) -> List[Dict]:
        """处理交易数据"""
        trades_list = []
        
        for _, trade in trades_df.iterrows():
            trade_info = {
                'timestamp': trade['timestamp'].isoformat() if hasattr(trade['timestamp'], 'isoformat') else str(trade['timestamp']),
                'side': int(trade['side']),
                'price': float(trade.get('price', 0)),
                'volume': float(trade.get('volume', 0)),
                'deal_vol_usdt': float(trade.get('deal_vol_usdt', 0)),
                'trade_id': trade.get('trade_id', ''),
                'order_id': trade.get('order_id', '')
            }
            trades_list.append(trade_info)
        
        return trades_list
    
    def _calculate_weighted_avg_price(self, trades_df: pd.DataFrame) -> float:
        """计算加权平均价格"""
        try:
            if len(trades_df) == 0:
                return 0.0
            
            if 'volume' not in trades_df.columns or 'price' not in trades_df.columns:
                return trades_df.get('price', pd.Series([0])).mean()
            
            total_volume = trades_df['volume'].sum()
            if total_volume == 0:
                return trades_df['price'].mean()
            
            weighted_price = (trades_df['price'] * trades_df['volume']).sum() / total_volume
            return float(weighted_price)
            
        except Exception as e:
            logger.error(f"计算加权平均价格失败: {e}")
            return 0.0
    
    def _calculate_position_pnl(self, opens: pd.DataFrame, closes: pd.DataFrame) -> float:
        """计算仓位盈亏"""
        try:
            if len(opens) == 0 or len(closes) == 0:
                return 0.0
            
            # 获取开仓和平仓的平均价格和总量
            open_avg_price = self._calculate_weighted_avg_price(opens)
            close_avg_price = self._calculate_weighted_avg_price(closes)
            
            open_volume = opens['volume'].sum() if 'volume' in opens.columns else 0
            close_volume = closes['volume'].sum() if 'volume' in closes.columns else 0
            
            if open_volume == 0 or close_volume == 0:
                # 如果没有数量信息，使用金额差估算
                open_amount = opens['deal_vol_usdt'].sum()
                close_amount = closes['deal_vol_usdt'].sum()
                return (close_amount - open_amount) * 0.001  # 简化计算
            
            # 使用最小量计算实际盈亏
            effective_volume = min(open_volume, close_volume)
            
            # 判断仓位方向
            open_side = opens['side'].iloc[0]
            
            if open_side == 1:  # 开多仓
                pnl = (close_avg_price - open_avg_price) * effective_volume
            elif open_side == 3:  # 开空仓
                pnl = (open_avg_price - close_avg_price) * effective_volume
            else:
                pnl = 0.0
            
            return float(pnl)
            
        except Exception as e:
            logger.error(f"计算仓位盈亏失败: {e}")
            return 0.0
    
    def _calculate_risk_metrics(self, profile: Dict) -> Dict:
        """计算风险指标"""
        try:
            metrics = {}
            
            # 仓位规模风险
            metrics['position_size_ratio'] = profile['total_open_amount'] / 10000  # 相对于10k基准
            
            # 持仓时间风险
            holding_hours = profile['duration_minutes'] / 60
            metrics['holding_duration_score'] = min(holding_hours / 24, 1.0)  # 标准化到24小时
            
            # 盈亏比率
            if profile['total_open_amount'] > 0:
                metrics['pnl_ratio'] = profile['realized_pnl'] / profile['total_open_amount']
            else:
                metrics['pnl_ratio'] = 0.0
            
            # 风险调整收益率（简化版夏普比率）
            if holding_hours > 0:
                annual_return = (profile['realized_pnl'] / profile['total_open_amount']) * (365 * 24 / holding_hours)
                metrics['risk_adjusted_return'] = annual_return - self.config['risk_free_rate']
            else:
                metrics['risk_adjusted_return'] = 0.0
            
            # 交易频率指标
            total_trades = len(profile['open_trades']) + len(profile['close_trades'])
            metrics['trade_frequency'] = total_trades / max(holding_hours, 1)  # 每小时交易次数
            
            # 价格偏离度（开平仓价格差异）
            if 'avg_open_price' in profile and 'avg_close_price' in profile:
                if profile['avg_open_price'] > 0:
                    metrics['price_deviation'] = abs(
                        profile['avg_close_price'] - profile['avg_open_price']
                    ) / profile['avg_open_price']
                else:
                    metrics['price_deviation'] = 0.0
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {}
    
    def _calculate_quality_indicators(self, profile: Dict, raw_group: pd.DataFrame) -> Dict:
        """计算数据质量指标"""
        try:
            indicators = {}
            
            # 数据完整性
            required_fields = ['timestamp', 'side', 'deal_vol_usdt']
            missing_fields = [f for f in required_fields if f not in raw_group.columns or raw_group[f].isna().any()]
            indicators['data_completeness'] = 1.0 - (len(missing_fields) / len(required_fields))
            
            # 时间序列连续性
            timestamps = pd.to_datetime(raw_group['timestamp']).sort_values()
            if len(timestamps) > 1:
                time_gaps = timestamps.diff().dropna()
                max_gap_hours = time_gaps.max().total_seconds() / 3600
                indicators['time_continuity'] = max(0.0, 1.0 - (max_gap_hours / 24))  # 24小时为基准
            else:
                indicators['time_continuity'] = 1.0
            
            # 交易逻辑一致性
            open_close_balance = abs(profile['total_open_amount'] - profile['total_close_amount'])
            if profile['total_open_amount'] > 0:
                indicators['balance_consistency'] = max(0.0, 1.0 - (open_close_balance / profile['total_open_amount']))
            else:
                indicators['balance_consistency'] = 0.0
            
            # 价格合理性
            if 'price' in raw_group.columns:
                prices = raw_group['price'].dropna()
                if len(prices) > 1:
                    price_volatility = prices.std() / prices.mean() if prices.mean() > 0 else 0
                    indicators['price_stability'] = max(0.0, 1.0 - min(price_volatility, 1.0))
                else:
                    indicators['price_stability'] = 1.0
            
            # 综合质量评分
            quality_scores = [v for v in indicators.values() if isinstance(v, (int, float))]
            indicators['overall_quality'] = np.mean(quality_scores) if quality_scores else 0.0
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算质量指标失败: {e}")
            return {'overall_quality': 0.0}
    
    def _validate_profile_completeness(self, profile: Dict) -> bool:
        """验证仓位画像完整性"""
        try:
            # 基本完整性检查
            if not profile.get('has_open', False):
                return False
            
            # 检查关键数据
            if profile.get('total_open_amount', 0) < self.config['min_position_amount']:
                return False
            
            # 检查数据质量
            quality_score = profile.get('quality_indicators', {}).get('overall_quality', 0.0)
            if quality_score < 0.5:  # 质量评分低于50%
                return False
            
            # 检查持仓时间合理性
            if profile.get('duration_minutes', 0) > self.config['max_holding_days'] * 24 * 60:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证仓位完整性失败: {e}")
            return False
    
    def _identify_missing_data(self, profile: Dict) -> List[str]:
        """识别缺失的数据"""
        missing = []
        
        if not profile.get('has_open', False):
            missing.append('开仓数据')
        
        if not profile.get('has_close', False):
            missing.append('平仓数据')
        
        if profile.get('total_open_amount', 0) == 0:
            missing.append('开仓金额')
        
        if not profile.get('open_time'):
            missing.append('开仓时间')
        
        if profile.get('is_complete', False) and not profile.get('close_time'):
            missing.append('平仓时间')
        
        quality_score = profile.get('quality_indicators', {}).get('overall_quality', 0.0)
        if quality_score < 0.5:
            missing.append('数据质量不足')
        
        return missing
    
    def get_profile_statistics(self) -> Dict:
        """获取仓位画像统计信息"""
        return {
            'processing_stats': self.profile_stats.copy(),
            'completeness_ratio': (
                self.profile_stats['complete_positions'] / 
                max(self.profile_stats['total_positions'], 1)
            ),
            'cache_size': len(self.position_cache)
        }
    
    def clear_cache(self):
        """清理缓存"""
        self.position_cache.clear()
        self.profile_stats = {
            'total_positions': 0,
            'complete_positions': 0,
            'incomplete_positions': 0,
            'processing_time': 0.0
        } 