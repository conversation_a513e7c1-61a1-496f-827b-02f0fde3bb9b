"""
任务ID生成器
生成包含日期范围和数据量的友好任务ID
格式：{12位UUID}_{日期范围}_{数据量}
例如：a1b2c3d4e5f6_1215-1218_15420
"""

import pandas as pd
import uuid
import logging
from datetime import datetime
from typing import Optional, Tuple

logger = logging.getLogger(__name__)

class TaskIdGenerator:
    """任务ID生成器"""
    
    @staticmethod
    def generate_task_id(df: pd.DataFrame = None, processing_mode: str = 'batch', filename: str = None,
                        actual_data_range: str = None, db_manager=None) -> str:
        """
        生成包含日期范围和数据量的任务ID

        参数:
            df: 数据DataFrame
            processing_mode: 处理模式 (batch/incremental/rerun)
            filename: 文件名（用于提取日期信息的备选方案）
            actual_data_range: 实际数据范围（用于修正任务ID中的日期范围）
            db_manager: 数据库管理器（用于获取累积时间范围）

        返回:
            格式化的任务ID
        """
        try:
            # 生成12位UUID
            short_uuid = str(uuid.uuid4()).replace('-', '')[:12]

            # 提取日期范围和数据量
            date_range, data_count = TaskIdGenerator._extract_data_info(df, filename)

            # 🚀 新功能：对于增量模式，使用累积时间范围
            if processing_mode == 'incremental' and db_manager:
                cumulative_range = TaskIdGenerator.get_cumulative_task_range(db_manager, df)
                if cumulative_range:
                    date_range = cumulative_range
                    logger.info(f"增量模式使用累积时间范围: {cumulative_range}")

            # 🔧 修复：如果提供了实际数据范围，使用实际范围替代提取的范围
            if actual_data_range:
                date_range = actual_data_range
                logger.info(f"使用实际数据范围覆盖任务ID日期: {actual_data_range}")

            # 构建任务ID - 使用中文标识符格式
            if date_range and data_count:
                task_id = f"{short_uuid}_日期{date_range}_数据量{data_count}"
            elif date_range:
                # 如果只有日期范围，没有数据量
                task_id = f"{short_uuid}_日期{date_range}"
            elif data_count:
                # 如果只有数据量，没有日期范围
                task_id = f"{short_uuid}_数据量{data_count}"
            else:
                # 如果都没有，使用基础格式
                task_id = short_uuid

            logger.info(f"生成任务ID: {task_id} (模式: {processing_mode})")
            return task_id

        except Exception as e:
            logger.warning(f"生成任务ID失败，使用默认格式: {str(e)}")
            return str(uuid.uuid4()).replace('-', '')[:12]
    
    @staticmethod
    def _extract_data_info(df: pd.DataFrame = None, filename: str = None) -> Tuple[Optional[str], Optional[str]]:
        """
        提取数据的日期范围和数据量信息
        
        返回:
            (日期范围字符串, 数据量字符串)
        """
        date_range = None
        data_count = None
        
        try:
            # 提取数据量
            if df is not None and len(df) > 0:
                count = len(df)
                if count >= 10000:
                    data_count = f"{count//1000}k"  # 15420 -> 15k
                else:
                    data_count = str(count)
            
            # 提取日期范围
            date_range = TaskIdGenerator._extract_date_range(df, filename)
            
        except Exception as e:
            logger.warning(f"提取数据信息失败: {str(e)}")
        
        return date_range, data_count
    
    @staticmethod
    def _extract_date_range(df: pd.DataFrame = None, filename: str = None) -> Optional[str]:
        """
        提取日期范围
        
        返回:
            日期范围字符串，格式如 "1215-1218"
        """
        try:
            # 方案1：从DataFrame中提取
            if df is not None and len(df) > 0:
                date_range = TaskIdGenerator._extract_date_from_dataframe(df)
                if date_range:
                    return date_range
            
            # 方案2：从文件名中提取
            if filename:
                date_range = TaskIdGenerator._extract_date_from_filename(filename)
                if date_range:
                    return date_range
            
        except Exception as e:
            logger.warning(f"提取日期范围失败: {str(e)}")
        
        return None
    
    @staticmethod
    def _extract_date_from_dataframe(df: pd.DataFrame) -> Optional[str]:
        """从DataFrame中提取日期范围"""
        try:
            # 查找可能的时间字段
            time_columns = []
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['time', 'date', 'create']):
                    time_columns.append(col)
            
            if not time_columns:
                return None
            
            # 尝试每个时间字段
            for time_col in time_columns:
                try:
                    # 转换为datetime (指定格式避免警告)
                    time_series = pd.to_datetime(
                        df[time_col],
                        format='%Y/%m/%d %H:%M:%S',
                        errors='coerce'
                    )
                    
                    # 过滤有效时间
                    valid_times = time_series.dropna()
                    if len(valid_times) == 0:
                        continue
                    
                    # 获取日期范围
                    min_date = valid_times.min().date()
                    max_date = valid_times.max().date()
                    
                    # 格式化为MMDD格式
                    if min_date == max_date:
                        # 同一天
                        return min_date.strftime("%m%d")
                    else:
                        # 日期范围
                        return f"{min_date.strftime('%m%d')}-{max_date.strftime('%m%d')}"
                        
                except Exception:
                    continue
            
        except Exception as e:
            logger.debug(f"从DataFrame提取日期失败: {str(e)}")
        
        return None
    
    @staticmethod
    def _extract_date_from_filename(filename: str) -> Optional[str]:
        """从文件名中提取日期信息"""
        try:
            import re

            # 匹配常见的日期格式
            patterns = [
                # YYYY-MM-DD 格式
                r'(\d{4})-(\d{1,2})-(\d{1,2})',
                # YYYYMMDD 格式
                r'(\d{4})(\d{2})(\d{2})',
                # 中文日期格式：12月15日
                r'(\d{1,2})月(\d{1,2})日',
                # 日期范围格式：12月15日-12月18日
                r'(\d{1,2})月(\d{1,2})日-(\d{1,2})月(\d{1,2})日',
                # 日期范围格式：12月15日-18日
                r'(\d{1,2})月(\d{1,2})日-(\d{1,2})日',
                # 下划线分隔的日期范围：20241215_20241218
                r'(\d{4})(\d{2})(\d{2})_(\d{4})(\d{2})(\d{2})',
            ]

            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, filename)
                if matches:
                    match = matches[0]

                    if i == 0 or i == 1:  # YYYY-MM-DD 或 YYYYMMDD 格式
                        if len(match) == 3:
                            if len(match[0]) == 4:  # YYYY-MM-DD格式
                                return f"{match[1].zfill(2)}{match[2].zfill(2)}"

                    elif i == 2:  # 中文格式：12月15日
                        if len(match) == 2:
                            return f"{match[0].zfill(2)}{match[1].zfill(2)}"

                    elif i == 3:  # 中文日期范围：12月15日-12月18日
                        if len(match) == 4:
                            start_date = f"{match[0].zfill(2)}{match[1].zfill(2)}"
                            end_date = f"{match[2].zfill(2)}{match[3].zfill(2)}"
                            return f"{start_date}-{end_date}"

                    elif i == 4:  # 中文日期范围：12月15日-18日
                        if len(match) == 3:
                            start_date = f"{match[0].zfill(2)}{match[1].zfill(2)}"
                            end_date = f"{match[0].zfill(2)}{match[2].zfill(2)}"  # 同月
                            return f"{start_date}-{end_date}"

                    elif i == 5:  # 数字日期范围：20241215_20241218
                        if len(match) == 6:
                            start_date = f"{match[1]}{match[2]}"  # MMDD
                            end_date = f"{match[4]}{match[5]}"    # MMDD
                            return f"{start_date}-{end_date}"

                    break

        except Exception as e:
            logger.debug(f"从文件名提取日期失败: {str(e)}")

        return None

    @staticmethod
    def get_database_actual_range(db_manager=None) -> Optional[str]:
        """
        获取数据库中实际的数据日期范围
        用于修正任务ID中的日期范围，确保任务ID反映真实的数据覆盖范围

        返回:
            实际数据范围字符串，格式如 "0101-1231" 或 None
        """
        if not db_manager:
            return None

        try:
            # 查询position_analysis表的实际数据范围 - 修复DuckDB语法
            sql = """
            SELECT
                MIN(CAST(open_time AS DATE)) as min_date,
                MAX(CAST(COALESCE(close_time, open_time) AS DATE)) as max_date
            FROM position_analysis
            WHERE open_time IS NOT NULL
            """

            result = db_manager.execute_sql(sql)
            if not result or not result[0]['min_date'] or not result[0]['max_date']:
                logger.info("数据库中暂无数据，无法获取实际数据范围")
                return None

            min_date = result[0]['min_date']
            max_date = result[0]['max_date']

            # 转换为MMDD格式
            if isinstance(min_date, str):
                from datetime import datetime
                min_date = datetime.fromisoformat(min_date).date()
                max_date = datetime.fromisoformat(max_date).date()

            if min_date == max_date:
                # 同一天
                return min_date.strftime("%m%d")
            else:
                # 日期范围
                return f"{min_date.strftime('%m%d')}-{max_date.strftime('%m%d')}"

        except Exception as e:
            logger.warning(f"获取数据库实际数据范围失败: {str(e)}")
            return None

    @staticmethod
    def get_cumulative_task_range(db_manager=None, current_df: pd.DataFrame = None) -> Optional[str]:
        """
        获取从第一次任务提交到当前任务的累积时间范围
        这个方法会查找历史任务记录，获取第一次提交的时间，结合当前数据的时间范围

        参数:
            db_manager: 数据库管理器
            current_df: 当前提交的数据DataFrame

        返回:
            累积时间范围字符串，格式如 "0101-1231" 或 None
        """
        if not db_manager:
            return None

        try:
            # 1. 获取第一次任务提交的时间范围
            first_task_range = TaskIdGenerator._get_first_task_date_range(db_manager)

            # 2. 获取当前数据的时间范围
            current_range = None
            if current_df is not None and len(current_df) > 0:
                current_range = TaskIdGenerator._extract_date_from_dataframe(current_df)

            # 3. 如果没有历史任务，使用当前数据范围
            if not first_task_range:
                logger.info("未找到历史任务记录，使用当前数据范围")
                return current_range

            # 4. 如果没有当前数据范围，使用历史范围
            if not current_range:
                logger.info("当前数据无时间信息，使用历史任务范围")
                return first_task_range

            # 5. 合并历史和当前的时间范围
            cumulative_range = TaskIdGenerator._merge_date_ranges(first_task_range, current_range)
            logger.info(f"累积时间范围: 历史[{first_task_range}] + 当前[{current_range}] = 累积[{cumulative_range}]")

            return cumulative_range

        except Exception as e:
            logger.warning(f"获取累积任务时间范围失败: {str(e)}")
            # 降级到原有逻辑
            return TaskIdGenerator.get_database_actual_range(db_manager)

    @staticmethod
    def _get_first_task_date_range(db_manager) -> Optional[str]:
        """
        获取第一次任务提交的日期范围
        通过解析最早的任务ID来获取时间信息
        """
        try:
            # 查询最早的合约分析任务
            sql = """
            SELECT task_id, created_at
            FROM tasks
            WHERE task_type = 'contract_analysis'
            ORDER BY created_at ASC
            LIMIT 1
            """

            result = db_manager.execute_sql(sql)
            if not result:
                logger.info("未找到历史合约分析任务")
                return None

            first_task_id = result[0]['task_id']
            logger.info(f"找到第一个任务: {first_task_id}")

            # 解析任务ID中的日期范围
            task_info = TaskIdGenerator.parse_task_id(first_task_id)
            first_date_range = task_info.get('date_range')

            if first_date_range:
                logger.info(f"第一个任务的日期范围: {first_date_range}")
                return first_date_range
            else:
                logger.warning(f"无法从第一个任务ID中提取日期范围: {first_task_id}")
                return None

        except Exception as e:
            logger.warning(f"获取第一个任务日期范围失败: {str(e)}")
            return None

    @staticmethod
    def _merge_date_ranges(first_range: str, current_range: str) -> str:
        """
        合并两个日期范围，返回从最早到最晚的完整范围

        参数:
            first_range: 第一个日期范围，如 "0101" 或 "0101-0105"
            current_range: 当前日期范围，如 "0110" 或 "0110-0115"

        返回:
            合并后的日期范围，如 "0101-0115"
        """
        try:
            # 解析第一个范围
            if '-' in first_range:
                first_start, first_end = first_range.split('-')
            else:
                first_start = first_end = first_range

            # 解析当前范围
            if '-' in current_range:
                current_start, current_end = current_range.split('-')
            else:
                current_start = current_end = current_range

            # 找到最早和最晚的日期
            all_dates = [first_start, first_end, current_start, current_end]
            # 简单的字符串比较（假设都是MMDD格式）
            min_date = min(all_dates)
            max_date = max(all_dates)

            # 返回合并后的范围
            if min_date == max_date:
                return min_date
            else:
                return f"{min_date}-{max_date}"

        except Exception as e:
            logger.warning(f"合并日期范围失败: {str(e)}")
            # 如果合并失败，返回当前范围
            return current_range

    @staticmethod
    def parse_task_id(task_id: str) -> dict:
        """
        解析任务ID，提取其中的信息
        支持新格式：fd465cfd5474_日期0721-0725_数据量15k
        也兼容旧格式：fd465cfd5474_0721-0725_15k

        返回:
            包含uuid、日期范围、数据量等信息的字典
        """
        try:
            import re

            result = {
                'uuid': '',
                'date_range': None,
                'data_count': None,
                'raw_task_id': task_id
            }

            # 新格式解析：使用正则表达式
            # 匹配格式：uuid_日期xxxx_数据量xxxx 或 uuid_日期xxxx 或 uuid_数据量xxxx
            new_format_pattern = r'^([a-f0-9]{12})(?:_日期([0-9-]+))?(?:_数据量([0-9]+k?))?$'
            new_match = re.match(new_format_pattern, task_id)

            if new_match:
                # 新格式
                result['uuid'] = new_match.group(1)
                result['date_range'] = new_match.group(2)
                result['data_count'] = new_match.group(3)
                return result

            # 旧格式解析：按下划线分割
            parts = task_id.split('_')
            result['uuid'] = parts[0] if len(parts) > 0 else task_id

            if len(parts) >= 2:
                # 判断第二部分是日期范围还是数据量
                second_part = parts[1]
                if re.match(r'^\d{4}(-\d{4})?$', second_part):
                    # 是日期范围（MMDD或MMDD-MMDD格式）
                    result['date_range'] = second_part
                elif re.match(r'^\d+k?$', second_part):
                    # 是数据量
                    result['data_count'] = second_part

            if len(parts) >= 3:
                # 第三部分是数据量
                result['data_count'] = parts[2]

            return result

        except Exception as e:
            logger.warning(f"解析任务ID失败: {str(e)}")
            return {
                'uuid': task_id,
                'date_range': None,
                'data_count': None,
                'raw_task_id': task_id
            }
