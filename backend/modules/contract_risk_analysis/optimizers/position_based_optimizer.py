"""
基于positionId的完整订单分析优化器
解决现有系统的核心问题：
1. profit计算错误 - 直接使用原始profit字段
2. 对敲检测低效 - 首笔开仓预筛选
3. 高频检测误判 - 区分正常加仓与异常高频
4. 数据利用不充分 - 构建完整订单生命周期
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class PositionTransaction:
    """单笔仓位交易记录 - 用于增量处理的交易序列构建"""
    position_id: str
    member_id: str
    contract_name: str
    timestamp: datetime
    side: int                    # 1=开多, 2=平空, 3=开空, 4=平多
    deal_vol: float             # 交易数量（原始值）
    signed_deal_vol: float      # 带符号的交易数量（+开仓，-平仓）
    deal_vol_usdt: float        # 交易金额
    deal_avg_price: float       # 成交价格
    sequence_index: int         # 在该position中的序号

    # 订单类型信息
    liquidity: str = ""         # taker/maker
    open_type: int = 1          # 1=逐仓, 2=全仓
    leverage: float = 1.0       # 杠杆倍数
    fee_usdt: float = 0.0       # 手续费

@dataclass
class CompletePosition:
    """完整订单（仓位）数据结构 - 支持增量处理和交易序列分析"""
    position_id: str
    member_id: str
    contract_name: str

    # 🚀 新增：交易序列（按时间排序）- 用于增量处理
    transactions: List[PositionTransaction]

    # 🚀 新增：累计统计 - 基于交易序列计算
    current_volume: float       # 当前持仓数量（累计）
    total_open_volume: float    # 总开仓数量
    total_close_volume: float   # 总平仓数量

    # 🚀 新增：时间分析（供高频算法使用）
    first_transaction_time: datetime
    last_transaction_time: datetime
    transaction_intervals: List[float]      # 每次交易间隔（分钟）[0, 0, 2, 3]
    transaction_directions: List[str]       # 对应的交易方向 ['open', 'open', 'close', 'close', 'open']

    # 🚀 新增：基础统计
    total_transactions: int                 # 总交易次数
    same_minute_transactions: int           # 同分钟内的交易次数

    # 🚀 新增：完整性判断
    is_position_complete: bool              # 基于数量判断是否完整
    needs_completion: bool                  # 是否需要补全
    missing_volume: float                   # 缺少的平仓数量

    # 开仓信息聚合（保持兼容性）
    first_open_time: datetime
    last_open_time: datetime
    total_open_amount: float
    avg_open_price: float
    open_trades_count: int
    primary_side: int  # 主要方向（1=多，3=空）

    # 平仓信息聚合（保持兼容性）
    first_close_time: Optional[datetime]
    last_close_time: Optional[datetime]
    total_close_amount: float
    avg_close_price: float
    close_trades_count: int

    # 订单特征（保持兼容性）
    is_completed: bool  # 是否完全平仓（兼容旧字段）
    total_duration_minutes: float  # 总持仓时长
    real_profit: float  # 真实盈亏（来自原始profit字段）
    calculated_profit: float  # 计算盈亏（价差×数量）

    # 交易行为特征（保持兼容性）
    is_quick_trade: bool  # 是否快进快出（<5分钟）
    is_scalping: bool  # 是否刷单行为
    add_position_count: int  # 加仓次数
    reduce_position_count: int  # 减仓次数

    # 风险指标（保持兼容性）
    risk_score: float  # 风险评分
    abnormal_flags: List[str]  # 异常标记

    # 杠杆和手续费信息（有默认值的字段放在最后）
    leverage: float = 1.0  # 杠杆倍数，默认1倍
    total_fee: float = 0.0  # 总手续费（来自fee_usdt字段）

    # 订单类型统计（基于liquidity字段：taker=市价单，maker=限价单）
    market_orders_open: int = 0    # 开仓市价单数量
    limit_orders_open: int = 0     # 开仓限价单数量
    market_orders_close: int = 0   # 平仓市价单数量
    limit_orders_close: int = 0    # 平仓限价单数量

    # 仓位模式标识（基于open_type字段：2=全仓，1=逐仓）
    cross_margin_positions: int = 0   # 是否为全仓模式（1=是，0=否）
    isolated_margin_positions: int = 0  # 是否为逐仓模式（1=是，0=否）

    # 🆕 新增字段：支持增量处理和数据合并（不影响现有逻辑）
    data_source: str = 'original'         # 数据来源：'original', 'merged_from_completion'
    historical_data: Optional[Dict] = None  # 从completion_status恢复的历史数据

    @property
    def duration_minutes(self) -> float:
        """持仓时长（分钟）- 兼容性属性"""
        return self.total_duration_minutes

    def calculate_signed_volume(self, side: int, deal_vol: float) -> float:
        """计算带符号的交易数量"""
        # side: 1=开多, 2=平空, 3=开空, 4=平多
        if side in [1, 3]:  # 开仓
            return deal_vol
        else:  # 平仓
            return -deal_vol

    def is_same_minute_transaction(self, prev_time: datetime, curr_time: datetime) -> bool:
        """判断两笔交易是否在同一分钟内"""
        return prev_time.replace(second=0, microsecond=0) == curr_time.replace(second=0, microsecond=0)

    def calculate_time_interval_minutes(self, prev_time: datetime, curr_time: datetime) -> float:
        """计算两笔交易的时间间隔（分钟）"""
        if self.is_same_minute_transaction(prev_time, curr_time):
            return 0.0
        else:
            return (curr_time - prev_time).total_seconds() / 60.0

    def get_transaction_direction(self, side: int) -> str:
        """获取交易方向字符串"""
        return 'open' if side in [1, 3] else 'close'


class PositionBasedOptimizer:
    """基于positionId的完整订单分析器"""
    
    def __init__(self):
        self.complete_positions: Dict[str, CompletePosition] = {}
        self.user_positions_map: Dict[str, List[str]] = defaultdict(list)
        self.contract_positions_map: Dict[str, List[str]] = defaultdict(list)
        self.time_window_index: Dict[str, List[str]] = defaultdict(list)
        
        # 🚀 第四代对敲检测配置参数 (硬编码配置)
        # 经过多轮优化和测试，这些参数提供了最佳的检测效果
        self.config = {
            # 基础检测参数
            'quick_trade_threshold_minutes': 5,    # 快进快出阈值（分钟）
            'scalping_trades_threshold': 10,       # 刷单交易次数阈值
            'high_frequency_threshold': 20,        # 高频交易阈值（笔/小时）

            # 🎯 对敲检测核心参数
            'wash_trading_time_window': 15,        # 对敲时间窗口（秒）- 同账户和跨账户统一
            'amount_match_tolerance': 0.05,        # 金额匹配容差（5%）- 相对容差
            'profit_hedge_threshold': 0.7,         # 盈亏对冲阈值（0.7）- 关键特征
            'wash_score_threshold': 0.7,           # 对敲综合评分阈值（0.7）
        }
    
    def build_complete_positions(self, df: pd.DataFrame) -> Dict[str, CompletePosition]:
        """
        构建完整的订单画像 - 兼容性方法
        核心：将散乱的交易记录重构为完整的订单生命周期

        注意：此方法现在调用新的序列构建逻辑以保持兼容性
        """
        logger.info(f"开始构建完整订单画像（兼容模式），数据量: {len(df)} 条记录")

        # 直接调用新的序列构建方法，使用batch模式保持兼容性
        return self.build_complete_positions_with_sequence(df, 'batch')



    def build_complete_positions_with_sequence(self, df: pd.DataFrame, processing_mode: str = 'batch') -> Dict[str, CompletePosition]:
        """
        构建完整订单 - 新的核心方法，支持增量处理

        关键改进：
        1. 使用新的交易序列构建逻辑
        2. 基于累计持仓数量判断完整性
        3. 为高频算法提供详细的时间数据

        参数:
            df: 交易数据DataFrame
            processing_mode: 处理模式 ('incremental' | 'rerun' | 'batch')

        返回:
            完整订单字典 {position_id: CompletePosition}
        """
        logger.info(f"开始构建完整订单序列，处理模式: {processing_mode}")

        if df.empty:
            logger.warning("输入数据为空")
            return {}

        complete_positions = {}

        # 🔧 在增量模式下，先检查已存在的完整订单，避免重复构建
        # 🆕 同时从数据库中批量读取相关的完整订单数据用于对敲检测
        existing_complete_positions = set()
        existing_positions_from_db = {}

        if processing_mode in ['incremental']:
            try:
                existing_positions_from_db = self._batch_load_related_positions(df)
                existing_complete_positions = set(existing_positions_from_db.keys())

                if existing_complete_positions:
                    logger.info(f"增量模式：批量加载了 {len(existing_complete_positions)} 个相关完整订单")
                    logger.debug(f"加载的订单ID: {list(existing_complete_positions)[:10]}{'...' if len(existing_complete_positions) > 10 else ''}")

            except Exception as e:
                logger.warning(f"批量加载相关订单失败: {str(e)}，将继续构建所有订单")
                existing_complete_positions = set()
                existing_positions_from_db = {}

        # 按position_id分组处理
        skipped_count = 0
        for position_id, position_group in df.groupby('position_id'):
            position_id_str = str(position_id)

            # 增量模式下跳过已存在的完整订单
            if processing_mode in ['incremental'] and position_id_str in existing_complete_positions:
                skipped_count += 1
                logger.debug(f"跳过已存在的完整订单: {position_id_str}")
                continue

            try:
                # 使用新的序列构建方法
                complete_position = self._build_complete_position_with_sequence(position_group, processing_mode)
                complete_positions[position_id_str] = complete_position

            except ValueError as e:
                # 数据验证错误，如果只有一个position则抛出，否则跳过
                if len(df['position_id'].unique()) == 1:
                    raise e  # 单个position的验证错误应该抛出
                else:
                    logger.warning(f"构建订单 {position_id} 失败: {str(e)}")
                    continue
            except Exception as e:
                logger.warning(f"构建订单 {position_id} 失败: {str(e)}")
                continue

        if skipped_count > 0:
            logger.info(f"增量模式：跳过构建 {skipped_count} 个已存在的完整订单")

        # 🆕 合并从数据库读取的已存在完整订单
        if existing_positions_from_db:
            complete_positions.update(existing_positions_from_db)
            logger.info(f"合并了 {len(existing_positions_from_db)} 个从数据库读取的完整订单")

        logger.info(f"完整订单序列构建完成，共 {len(complete_positions)} 个订单")
        return complete_positions

    def _convert_db_row_to_complete_position(self, row: Dict) -> Optional[CompletePosition]:
        """
        将数据库行数据转换为CompletePosition对象
        用于增量模式下从position_analysis表读取已存在的完整订单

        参数:
            row: 数据库查询结果行

        返回:
            CompletePosition对象或None
        """
        try:
            # 解析时间字段
            open_time = row.get('open_time')
            close_time = row.get('close_time')

            if isinstance(open_time, str):
                open_time = datetime.fromisoformat(open_time.replace('Z', '+00:00'))
            if isinstance(close_time, str):
                close_time = datetime.fromisoformat(close_time.replace('Z', '+00:00'))

            # 创建简化的交易记录（用于对敲检测）
            # 由于数据库中没有详细的交易序列，我们创建简化的开仓和平仓记录
            open_amount = float(row.get('total_open_amount', 0))
            close_amount = float(row.get('total_close_amount', 0))

            open_transaction = PositionTransaction(
                position_id=row['position_id'],
                member_id=row['member_id'],
                contract_name=row['contract_name'],
                timestamp=open_time,
                side=row.get('primary_side', 1),
                deal_vol=open_amount / float(row.get('avg_open_price', 1)) if row.get('avg_open_price', 0) > 0 else 0,
                signed_deal_vol=open_amount,
                deal_vol_usdt=open_amount,
                deal_avg_price=float(row.get('avg_open_price', 0)),
                sequence_index=0
            )

            close_transaction = PositionTransaction(
                position_id=row['position_id'],
                member_id=row['member_id'],
                contract_name=row['contract_name'],
                timestamp=close_time,
                side=4 if row.get('primary_side', 1) == 1 else 2,  # 1->4(平多), 3->2(平空)
                deal_vol=close_amount / float(row.get('avg_close_price', 1)) if row.get('avg_close_price', 0) > 0 else 0,
                signed_deal_vol=-close_amount,  # 平仓为负值
                deal_vol_usdt=close_amount,
                deal_avg_price=float(row.get('avg_close_price', 0)),
                sequence_index=1
            )

            transactions = [open_transaction, close_transaction]

            # 计算持仓时长
            duration_minutes = float(row.get('duration_minutes', 0))
            if duration_minutes == 0 and open_time and close_time:
                duration_minutes = (close_time - open_time).total_seconds() / 60

            # 创建CompletePosition对象，提供所有必需的字段
            complete_position = CompletePosition(
                position_id=row['position_id'],
                member_id=row['member_id'],
                contract_name=row['contract_name'],

                # 交易序列（新字段）
                transactions=transactions,
                current_volume=0,  # 已完成订单，当前持仓为0
                total_open_volume=open_amount,
                total_close_volume=close_amount,

                # 时间信息（新字段）
                first_transaction_time=open_time,
                last_transaction_time=close_time,
                transaction_intervals=[duration_minutes] if duration_minutes > 0 else [0],
                transaction_directions=['open', 'close'],

                # 统计信息（新字段）
                total_transactions=2,  # 简化处理：开仓+平仓
                same_minute_transactions=0,  # 简化处理
                is_position_complete=True,  # 从数据库读取的都是完整订单
                needs_completion=False,
                missing_volume=0,

                # 开仓信息聚合（兼容性字段）- 🔧 修复：确保数值字段转换为float类型
                first_open_time=open_time,
                last_open_time=open_time,
                total_open_amount=float(open_amount) if open_amount is not None else 0.0,
                avg_open_price=float(row.get('avg_open_price', 0)) if row.get('avg_open_price') is not None else 0.0,
                open_trades_count=1,
                primary_side=row.get('primary_side', 1),

                # 平仓信息聚合（兼容性字段）- 🔧 修复：确保数值字段转换为float类型
                first_close_time=close_time,
                last_close_time=close_time,
                total_close_amount=float(close_amount) if close_amount is not None else 0.0,
                avg_close_price=float(row.get('avg_close_price', 0)) if row.get('avg_close_price') is not None else 0.0,
                close_trades_count=1 if close_amount > 0 else 0,

                # 订单特征（兼容性字段）- 🔧 修复：确保数值字段转换为float类型
                is_completed=True,  # 从数据库读取的都是已完成订单
                total_duration_minutes=float(duration_minutes) if duration_minutes is not None else 0.0,
                real_profit=float(row.get('net_pnl', 0)) if row.get('net_pnl') is not None else 0.0,
                calculated_profit=float(row.get('total_pnl', 0)) if row.get('total_pnl') is not None else 0.0,

                # 交易行为特征（兼容性字段）
                is_quick_trade=duration_minutes < 5,
                is_scalping=False,  # 简化处理
                add_position_count=0,  # 简化处理
                reduce_position_count=0,  # 简化处理

                # 风险指标（兼容性字段）
                risk_score=0.0,  # 简化处理
                abnormal_flags=[],  # 简化处理

                # 数据来源标记
                data_source='from_database',
                historical_data=row
            )

            # 设置兼容性属性（用于对敲检测算法）- 🔧 修复：确保所有数值字段都转换为float类型
            complete_position.primary_side = row.get('primary_side', 1)
            complete_position.first_open_time = open_time
            complete_position.last_close_time = close_time
            complete_position.total_open_amount = float(row.get('total_open_amount', 0)) if row.get('total_open_amount') is not None else 0.0
            complete_position.total_close_amount = float(row.get('total_close_amount', 0)) if row.get('total_close_amount') is not None else 0.0
            complete_position.real_profit = float(row.get('net_pnl', 0)) if row.get('net_pnl') is not None else 0.0  # 使用net_pnl作为real_profit
            complete_position.total_duration_minutes = float(duration_minutes) if duration_minutes is not None else 0.0
            complete_position.is_completed = True  # 从数据库读取的都是已完成订单
            complete_position.first_close_time = close_time  # 添加这个属性用于对敲检测

            # 🔧 修复：确保其他可能的数值字段也转换为float类型
            complete_position.total_fee = float(getattr(complete_position, 'total_fee', 0)) if getattr(complete_position, 'total_fee', 0) is not None else 0.0
            complete_position.leverage = float(getattr(complete_position, 'leverage', 1)) if getattr(complete_position, 'leverage', 1) is not None else 1.0
            complete_position.avg_open_price = float(getattr(complete_position, 'avg_open_price', 0)) if getattr(complete_position, 'avg_open_price', 0) is not None else 0.0
            complete_position.avg_close_price = float(getattr(complete_position, 'avg_close_price', 0)) if getattr(complete_position, 'avg_close_price', 0) is not None else 0.0

            logger.debug(f"成功转换订单 {row['position_id']} 从数据库到CompletePosition对象")
            return complete_position

        except Exception as e:
            logger.error(f"转换数据库行到CompletePosition失败: {str(e)}, 行数据: {row}")
            return None

    def _batch_load_related_positions(self, df: pd.DataFrame) -> Dict[str, CompletePosition]:
        """
        高效批量加载相关的已存在完整订单

        策略：
        1. 首先查询输入数据中的 position_id 是否已存在
        2. 然后基于这些订单的用户和合约信息，批量查询相关订单
        3. 使用分批查询避免SQL参数过多的问题

        参数:
            df: 当前输入的交易数据

        返回:
            相关完整订单字典 {position_id: CompletePosition}
        """
        try:
            from database.duckdb_manager import DuckDBManager
            db_manager = DuckDBManager()

            # 第一步：检查输入的 position_id 是否已存在
            input_position_ids = df['position_id'].unique().tolist()
            input_position_ids_str = [str(pid) for pid in input_position_ids]

            existing_positions = {}

            # 分批查询，避免SQL参数过多
            batch_size = 1000
            for i in range(0, len(input_position_ids_str), batch_size):
                batch_ids = input_position_ids_str[i:i + batch_size]
                placeholders = ','.join(['?' for _ in batch_ids])

                sql = f"""
                SELECT position_id, member_id, contract_name, primary_side,
                       open_time, close_time, total_open_amount, total_close_amount,
                       total_pnl, net_pnl, avg_open_price, avg_close_price,
                       duration_minutes
                FROM position_analysis
                WHERE position_id IN ({placeholders})
                AND close_time IS NOT NULL
                """

                batch_results = db_manager.execute_sql(sql, batch_ids)

                if batch_results:
                    for row in batch_results:
                        complete_position = self._convert_db_row_to_complete_position(row)
                        if complete_position:
                            existing_positions[row['position_id']] = complete_position

            logger.info(f"第一步：从输入的 {len(input_position_ids_str)} 个position_id中找到 {len(existing_positions)} 个已存在的完整订单")

            # 第二步：基于已存在订单的用户和合约信息，查询更多相关订单
            if existing_positions:
                # 提取用户和合约信息
                member_ids = list(set(pos.member_id for pos in existing_positions.values()))
                contract_names = list(set(pos.contract_name for pos in existing_positions.values()))

                # 计算时间范围（基于已存在订单的时间范围）
                open_times = [pos.first_open_time for pos in existing_positions.values()]
                min_time = min(open_times) - pd.Timedelta(hours=6)  # 前后各6小时
                max_time = max(open_times) + pd.Timedelta(hours=6)

                logger.info(f"第二步：基于 {len(member_ids)} 个用户和 {len(contract_names)} 个合约查询相关订单")

                # 分批查询相关订单
                related_positions = self._batch_query_related_orders(
                    db_manager, member_ids, contract_names, min_time, max_time,
                    exclude_position_ids=set(existing_positions.keys())
                )

                # 合并结果
                existing_positions.update(related_positions)
                logger.info(f"第二步：额外加载了 {len(related_positions)} 个相关订单")

            logger.info(f"批量加载完成：总共加载了 {len(existing_positions)} 个完整订单用于对敲检测")
            return existing_positions

        except Exception as e:
            logger.error(f"批量加载相关订单失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {}

    def _batch_query_related_orders(self, db_manager, member_ids: List[str], contract_names: List[str],
                                   min_time, max_time, exclude_position_ids: set) -> Dict[str, CompletePosition]:
        """
        批量查询相关订单

        参数:
            db_manager: 数据库管理器
            member_ids: 用户ID列表
            contract_names: 合约名称列表
            min_time: 最小时间
            max_time: 最大时间
            exclude_position_ids: 要排除的position_id集合

        返回:
            相关订单字典
        """
        related_positions = {}

        try:
            # 分批查询用户相关订单（用于同账户对敲检测）
            batch_size = 100
            for i in range(0, len(member_ids), batch_size):
                batch_members = member_ids[i:i + batch_size]
                member_placeholders = ','.join(['?' for _ in batch_members])

                sql = f"""
                SELECT position_id, member_id, contract_name, primary_side,
                       open_time, close_time, total_open_amount, total_close_amount,
                       total_pnl, net_pnl, avg_open_price, avg_close_price,
                       duration_minutes
                FROM position_analysis
                WHERE member_id IN ({member_placeholders})
                AND close_time IS NOT NULL
                AND open_time >= ?
                AND open_time <= ?
                AND total_open_amount > 100
                ORDER BY open_time DESC
                LIMIT 1000
                """

                params = batch_members + [min_time, max_time]
                batch_results = db_manager.execute_sql(sql, params)

                if batch_results:
                    for row in batch_results:
                        if row['position_id'] not in exclude_position_ids:
                            complete_position = self._convert_db_row_to_complete_position(row)
                            if complete_position:
                                related_positions[row['position_id']] = complete_position

            # 分批查询合约相关订单（用于跨账户对敲检测）
            for i in range(0, len(contract_names), batch_size):
                batch_contracts = contract_names[i:i + batch_size]
                contract_placeholders = ','.join(['?' for _ in batch_contracts])

                sql = f"""
                SELECT position_id, member_id, contract_name, primary_side,
                       open_time, close_time, total_open_amount, total_close_amount,
                       total_pnl, net_pnl, avg_open_price, avg_close_price,
                       duration_minutes
                FROM position_analysis
                WHERE contract_name IN ({contract_placeholders})
                AND close_time IS NOT NULL
                AND open_time >= ?
                AND open_time <= ?
                AND total_open_amount > 100
                ORDER BY open_time DESC
                LIMIT 2000
                """

                params = batch_contracts + [min_time, max_time]
                batch_results = db_manager.execute_sql(sql, params)

                if batch_results:
                    for row in batch_results:
                        if row['position_id'] not in exclude_position_ids and row['position_id'] not in related_positions:
                            complete_position = self._convert_db_row_to_complete_position(row)
                            if complete_position:
                                related_positions[row['position_id']] = complete_position

            logger.info(f"相关订单查询完成：用户相关订单和合约相关订单共 {len(related_positions)} 个")
            return related_positions

        except Exception as e:
            logger.error(f"批量查询相关订单失败: {str(e)}")
            return {}

    def _build_complete_position_with_sequence(self, position_group: pd.DataFrame, processing_mode: str = 'batch') -> CompletePosition:
        """
        构建单个完整订单的交易序列 - 优化版本

        核心逻辑：
        1. 按时间排序构建交易序列
        2. 计算累计持仓变化
        3. 记录时间间隔和方向信息
        4. 精确判断完整性
        5. 处理边界情况和异常数据

        参数:
            position_group: 单个position_id的所有交易记录
            processing_mode: 处理模式 ('incremental' | 'rerun' | 'batch')

        返回:
            CompletePosition对象
        """
        if position_group.empty:
            raise ValueError("交易记录为空")

        # 数据验证和清理
        position_group = self._validate_and_clean_position_data(position_group)

        # 按时间排序
        sorted_trades = position_group.sort_values('create_time')

        # 基础信息提取和验证
        position_id = str(sorted_trades.iloc[0]['position_id'])
        member_id = str(sorted_trades.iloc[0]['member_id'])
        contract_name = str(sorted_trades.iloc[0]['contract_name'])

        # 验证position_id一致性
        if not all(sorted_trades['position_id'].astype(str) == position_id):
            logger.warning(f"Position {position_id} 包含不一致的position_id")
            # 尝试修复：统一转换为字符串并过滤
            sorted_trades = sorted_trades[sorted_trades['position_id'].astype(str) == position_id]
            if sorted_trades.empty:
                raise ValueError(f"Position {position_id} 修复后无有效记录")

        logger.debug(f"构建订单序列: {position_id}, 交易数: {len(sorted_trades)}")

        # 构建交易序列
        transactions = []
        current_volume = 0.0
        transaction_intervals = []
        transaction_directions = []
        same_minute_count = 0

        # 统计信息
        total_open_volume = 0.0
        total_close_volume = 0.0
        total_open_amount = 0.0
        total_close_amount = 0.0
        open_trades_count = 0
        close_trades_count = 0

        # 价格和时间信息
        open_prices = []
        close_prices = []
        open_times = []
        close_times = []

        # 优化的交易序列构建循环
        for idx, (_, trade) in enumerate(sorted_trades.iterrows()):
            try:
                # 提取和验证交易数据
                side = int(trade['side'])
                deal_vol = float(trade['deal_vol'])

                # 使用优化的带符号数量计算
                signed_volume = self._calculate_signed_volume_optimized(side, deal_vol)

                # 安全地提取可选字段
                deal_vol_usdt = float(trade.get('deal_vol_usdt', 0))
                deal_avg_price = float(trade.get('deal_avg_price', 0))
                liquidity = str(trade.get('liquidity', ''))
                open_type = int(trade.get('open_type', 1))
                leverage = float(trade.get('leverage', 1.0))
                fee_usdt = float(trade.get('fee_usdt', 0))

                # 🚀 安全处理时间字段
                create_time = trade['create_time']
                if create_time is None or pd.isna(create_time):
                    logger.error(f"订单 {position_id} 的交易记录时间为None或NaT，跳过此记录")
                    continue

                # 确保时间是datetime类型
                if not isinstance(create_time, pd.Timestamp):
                    try:
                        create_time = pd.to_datetime(create_time)
                    except Exception as e:
                        logger.error(f"订单 {position_id} 时间转换失败: {e}，跳过此记录")
                        continue

                # 创建交易记录
                transaction = PositionTransaction(
                    position_id=position_id,
                    member_id=member_id,
                    contract_name=contract_name,
                    timestamp=create_time,
                    side=side,
                    deal_vol=deal_vol,
                    signed_deal_vol=signed_volume,
                    deal_vol_usdt=deal_vol_usdt,
                    deal_avg_price=deal_avg_price,
                    sequence_index=idx,
                    liquidity=liquidity,
                    open_type=open_type,
                    leverage=leverage,
                    fee_usdt=fee_usdt
                )

                transactions.append(transaction)
                current_volume += signed_volume

                # 记录交易方向
                direction = 'open' if side in [1, 3] else 'close'
                transaction_directions.append(direction)

                # 统计开仓/平仓信息
                if side in [1, 3]:  # 开仓（开多、开空）
                    total_open_volume += deal_vol
                    total_open_amount += deal_vol_usdt
                    open_trades_count += 1
                    if deal_avg_price > 0:  # 只记录有效价格
                        open_prices.append(deal_avg_price)
                    open_times.append(transaction.timestamp)
                else:  # 平仓
                    total_close_volume += deal_vol
                    total_close_amount += deal_vol_usdt
                    close_trades_count += 1
                    if deal_avg_price > 0:  # 只记录有效价格
                        close_prices.append(deal_avg_price)
                    close_times.append(transaction.timestamp)

                # 计算时间间隔
                if idx > 0:
                    prev_time = transactions[idx-1].timestamp
                    curr_time = transaction.timestamp

                    # 使用优化的时间间隔计算
                    time_interval = self._calculate_time_interval_optimized(prev_time, curr_time)
                    transaction_intervals.append(time_interval)

                    # 统计同分钟交易（当前交易与前一笔在同分钟内）
                    if time_interval == 0.0:
                        same_minute_count += 1

            except Exception as e:
                logger.warning(f"处理交易记录 {idx} 失败: {str(e)}")
                continue

        # 计算完整性
        is_complete = abs(current_volume) <= 1  # 容差为1
        needs_completion = not is_complete
        missing_volume = current_volume if current_volume > 0 else 0

        # 计算平均价格
        avg_open_price = np.mean(open_prices) if open_prices else 0.0
        avg_close_price = np.mean(close_prices) if close_prices else 0.0

        # 时间信息 - 增强安全检查
        first_transaction_time = transactions[0].timestamp
        last_transaction_time = transactions[-1].timestamp

        # 🚀 最终兜底：如果没有有效交易记录，抛出错误
        if not transactions:
            raise ValueError(f"订单 {position_id} 没有有效的交易记录")

        # 🚀 最终兜底：如果第一笔交易时间仍为None或NaT，抛出错误
        if first_transaction_time is None or pd.isna(first_transaction_time):
            raise ValueError(f"订单 {position_id} 第一笔交易时间为None或NaT，无法处理")
        if last_transaction_time is None or pd.isna(last_transaction_time):
            last_transaction_time = first_transaction_time

        # 🚀 智能开仓时间处理：如果没有开仓记录，推算开仓时间
        if open_times:
            first_open_time = open_times[0]
            last_open_time = open_times[-1]
            logger.debug(f"订单 {position_id} 使用真实开仓时间: {first_open_time}")
        else:
            # 🔧 修复：在增量模式下，如果没有开仓记录，不进行推算，交给补全状态处理器处理
            if processing_mode in ['incremental', 'rerun'] and close_times:
                # 增量模式下，只有平仓数据的情况交给补全状态处理器处理
                # 这里使用平仓时间作为临时开仓时间，避免后续处理出错
                first_open_time = close_times[0]
                last_open_time = close_times[0]
                logger.debug(f"订单 {position_id} 在增量模式下缺少开仓记录，使用平仓时间作为临时开仓时间，等待补全状态处理器处理")
            elif close_times:
                # 非增量模式下，根据平仓时间推算开仓时间
                estimated_duration = timedelta(minutes=30)  # 默认估算30分钟持仓时长
                first_open_time = close_times[0] - estimated_duration
                last_open_time = first_open_time
                logger.warning(f"订单 {position_id} 缺少开仓记录，推算开仓时间: {first_open_time} (基于平仓时间: {close_times[0]})")
            else:
                # 既没有开仓也没有平仓记录，使用第一笔交易时间
                first_open_time = first_transaction_time
                last_open_time = first_transaction_time
                logger.warning(f"订单 {position_id} 既无开仓也无平仓记录，使用第一笔交易时间: {first_open_time}")

        # 🚀 最终安全检查：确保开仓时间不为None或NaT
        if first_open_time is None or pd.isna(first_open_time):
            raise ValueError(f"订单 {position_id} 开仓时间为None或NaT，推算逻辑失败")

        first_close_time = close_times[0] if close_times else None
        last_close_time = close_times[-1] if close_times else None

        # 持仓时长
        duration_minutes = (last_transaction_time - first_transaction_time).total_seconds() / 60.0

        # 判断主要方向（基于开仓量）
        long_volume = sum(t.deal_vol for t in transactions if t.side == 1)
        short_volume = sum(t.deal_vol for t in transactions if t.side == 3)
        primary_side = 1 if long_volume >= short_volume else 3

        # 创建CompletePosition对象
        complete_position = CompletePosition(
            position_id=position_id,
            member_id=member_id,
            contract_name=contract_name,

            # 新增字段
            transactions=transactions,
            current_volume=current_volume,
            total_open_volume=total_open_volume,
            total_close_volume=total_close_volume,
            first_transaction_time=first_transaction_time,
            last_transaction_time=last_transaction_time,
            transaction_intervals=transaction_intervals,
            transaction_directions=transaction_directions,
            total_transactions=len(transactions),
            same_minute_transactions=same_minute_count,
            is_position_complete=is_complete,
            needs_completion=needs_completion,
            missing_volume=missing_volume,

            # 兼容字段
            first_open_time=first_open_time,
            last_open_time=last_open_time,
            total_open_amount=total_open_amount,
            avg_open_price=avg_open_price,
            open_trades_count=open_trades_count,
            primary_side=primary_side,
            first_close_time=first_close_time,
            last_close_time=last_close_time,
            total_close_amount=total_close_amount,
            avg_close_price=avg_close_price,
            close_trades_count=close_trades_count,
            is_completed=is_complete,  # 兼容旧字段
            total_duration_minutes=duration_minutes,
            real_profit=float(sorted_trades['profit'].sum()) if 'profit' in sorted_trades.columns else 0.0,  # 🔧 使用原始profit字段，确保为float类型
            calculated_profit=0.0,  # 待计算
            is_quick_trade=duration_minutes < 5,
            is_scalping=len(transactions) > 10,
            add_position_count=max(0, open_trades_count - 1),
            reduce_position_count=close_trades_count,
            risk_score=0.5,  # 待计算
            abnormal_flags=[]
        )

        return complete_position



    def _is_same_minute(self, time1: datetime, time2: datetime) -> bool:
        """判断两个时间是否在同一分钟内"""
        return time1.replace(second=0, microsecond=0) == time2.replace(second=0, microsecond=0)

    def _validate_and_clean_position_data(self, position_group: pd.DataFrame) -> pd.DataFrame:
        """
        验证和清理单个position的交易数据

        参数:
            position_group: 单个position的交易记录

        返回:
            清理后的DataFrame
        """
        # 必要字段检查
        required_fields = ['position_id', 'member_id', 'contract_name', 'side', 'deal_vol', 'create_time']
        missing_fields = [field for field in required_fields if field not in position_group.columns]

        if missing_fields:
            raise ValueError(f"缺少必要字段: {missing_fields}")

        # 数据类型转换和验证
        cleaned_data = position_group.copy()

        # 时间字段验证 - 应该已经在入口处标准化为datetime格式
        if not pd.api.types.is_datetime64_any_dtype(cleaned_data['create_time']):
            logger.error("时间字段未正确标准化，应该在数据入口处已转换为datetime格式")
            raise ValueError("时间字段格式错误，应该在数据入口处已标准化")

        # 数值字段处理
        numeric_fields = ['side', 'deal_vol', 'deal_vol_usdt', 'deal_avg_price', 'leverage', 'fee_usdt']
        for field in numeric_fields:
            if field in cleaned_data.columns:
                cleaned_data[field] = pd.to_numeric(cleaned_data[field], errors='coerce')

        # 移除无效记录
        before_count = len(cleaned_data)
        cleaned_data = cleaned_data.dropna(subset=['side', 'deal_vol'])
        cleaned_data = cleaned_data[cleaned_data['deal_vol'] > 0]  # 交易量必须大于0
        cleaned_data = cleaned_data[cleaned_data['side'].isin([1, 2, 3, 4])]  # 有效的side值

        after_count = len(cleaned_data)
        if after_count < before_count:
            logger.warning(f"清理数据：{before_count} -> {after_count} 条记录")

        if cleaned_data.empty:
            raise ValueError("清理后无有效交易记录")

        return cleaned_data

    def _calculate_signed_volume_optimized(self, side: int, deal_vol: float) -> float:
        """
        优化的带符号交易量计算 - 增量处理核心算法

        交易方向映射：
        - 1 = 开多仓 → +deal_vol (增加持仓)
        - 2 = 平空仓 → -deal_vol (减少持仓)
        - 3 = 开空仓 → +deal_vol (增加持仓)
        - 4 = 平多仓 → -deal_vol (减少持仓)

        参数:
            side: 交易方向 (1=开多, 2=平空, 3=开空, 4=平多)
            deal_vol: 交易数量

        返回:
            带符号的交易数量（+开仓，-平仓）
        """
        # 输入验证和类型转换
        try:
            side = int(side)
            deal_vol = float(deal_vol)
        except (ValueError, TypeError) as e:
            raise ValueError(f"无效的参数类型或值: side={side}, deal_vol={deal_vol}, 错误: {str(e)}")

        # 数量验证
        if deal_vol < 0:
            logger.warning(f"交易数量为负值: {deal_vol}")
            deal_vol = abs(deal_vol)

        if deal_vol == 0:
            return 0.0

        # 方向计算
        if side in [1, 3]:  # 开仓（开多、开空）
            return deal_vol
        elif side in [2, 4]:  # 平仓（平空、平多）
            return -deal_vol
        else:
            logger.warning(f"未知的交易方向: {side}，默认为开仓")
            return deal_vol  # 默认为开仓，避免数据丢失

    def calculate_position_volume_sequence(self, transactions: List) -> List[float]:
        """
        计算持仓数量变化序列

        示例：
        交易序列: [开多100, 加仓200, 平多100, 平多50, 加仓100]
        数量序列: [+100, +200, -100, -50, +100]
        累计序列: [100, 300, 200, 150, 250]

        参数:
            transactions: PositionTransaction列表

        返回:
            累计持仓数量序列
        """
        if not transactions:
            return []

        cumulative_volumes = []
        current_volume = 0.0

        for transaction in transactions:
            current_volume += transaction.signed_deal_vol
            cumulative_volumes.append(current_volume)

        return cumulative_volumes

    def validate_position_completeness(self, final_volume: float, tolerance: float = 1.0) -> dict:
        """
        验证订单完整性

        参数:
            final_volume: 最终持仓数量
            tolerance: 容差值（默认1.0）

        返回:
            完整性分析结果
        """
        is_complete = abs(final_volume) <= tolerance

        return {
            'is_complete': is_complete,
            'final_volume': final_volume,
            'needs_completion': not is_complete,
            'missing_volume': final_volume if final_volume > 0 else 0,
            'excess_volume': abs(final_volume) if final_volume < 0 else 0,
            'tolerance_used': tolerance,
            'completeness_ratio': 1.0 - (abs(final_volume) / max(abs(final_volume) + tolerance, tolerance))
        }

    def analyze_position_lifecycle(self, transactions: List) -> dict:
        """
        分析订单生命周期和持仓变化模式

        参数:
            transactions: PositionTransaction列表

        返回:
            生命周期分析结果
        """
        if not transactions:
            return {'error': '无交易记录'}

        # 计算累计持仓序列
        cumulative_volumes = self.calculate_position_volume_sequence(transactions)

        # 分析持仓变化模式
        max_position = max(cumulative_volumes)
        min_position = min(cumulative_volumes)
        final_position = cumulative_volumes[-1]

        # 统计开仓和平仓操作
        open_operations = sum(1 for t in transactions if t.signed_deal_vol > 0)
        close_operations = sum(1 for t in transactions if t.signed_deal_vol < 0)

        # 计算持仓波动性
        position_changes = [abs(cumulative_volumes[i] - cumulative_volumes[i-1])
                           for i in range(1, len(cumulative_volumes))]
        avg_position_change = sum(position_changes) / len(position_changes) if position_changes else 0

        # 识别交易模式
        trading_pattern = self._identify_trading_pattern(transactions, cumulative_volumes)

        return {
            'total_transactions': len(transactions),
            'open_operations': open_operations,
            'close_operations': close_operations,
            'max_position': max_position,
            'min_position': min_position,
            'final_position': final_position,
            'position_range': max_position - min_position,
            'avg_position_change': avg_position_change,
            'cumulative_sequence': cumulative_volumes,
            'trading_pattern': trading_pattern,
            'lifecycle_stages': self._identify_lifecycle_stages(cumulative_volumes)
        }

    def _identify_trading_pattern(self, transactions: List, cumulative_volumes: List[float]) -> dict:
        """
        识别交易模式

        参数:
            transactions: 交易列表
            cumulative_volumes: 累计持仓序列

        返回:
            交易模式分析
        """
        if len(transactions) < 2:
            return {'pattern': 'single_trade', 'confidence': 1.0}

        # 分析模式特征
        is_scalping = len(transactions) > 10  # 高频刷单
        is_pyramid = self._is_pyramid_trading(transactions)  # 金字塔加仓
        is_averaging = self._is_averaging_down(transactions)  # 摊平成本
        is_complete_cycle = abs(cumulative_volumes[-1]) <= 1  # 完整周期

        # 计算持仓时长
        duration_minutes = (transactions[-1].timestamp - transactions[0].timestamp).total_seconds() / 60
        is_quick_trade = duration_minutes < 5  # 快进快出

        # 模式识别
        if is_complete_cycle and is_quick_trade:
            pattern = 'quick_complete_cycle'
            confidence = 0.9
        elif is_scalping:
            pattern = 'high_frequency_scalping'
            confidence = 0.8
        elif is_pyramid:
            pattern = 'pyramid_trading'
            confidence = 0.7
        elif is_averaging:
            pattern = 'averaging_strategy'
            confidence = 0.7
        elif is_complete_cycle:
            pattern = 'complete_trading_cycle'
            confidence = 0.8
        else:
            pattern = 'incomplete_position'
            confidence = 0.6

        return {
            'pattern': pattern,
            'confidence': confidence,
            'features': {
                'is_scalping': is_scalping,
                'is_pyramid': is_pyramid,
                'is_averaging': is_averaging,
                'is_complete_cycle': is_complete_cycle,
                'is_quick_trade': is_quick_trade,
                'duration_minutes': duration_minutes
            }
        }

    def _is_pyramid_trading(self, transactions: List) -> bool:
        """判断是否为金字塔加仓模式"""
        open_transactions = [t for t in transactions if t.signed_deal_vol > 0]
        if len(open_transactions) < 2:
            return False

        # 检查是否逐步减少加仓量
        volumes = [t.deal_vol for t in open_transactions]
        decreasing_count = sum(1 for i in range(1, len(volumes)) if volumes[i] < volumes[i-1])

        return decreasing_count >= len(volumes) * 0.6  # 60%以上递减

    def _is_averaging_down(self, transactions: List) -> bool:
        """判断是否为摊平成本模式"""
        open_transactions = [t for t in transactions if t.signed_deal_vol > 0]
        if len(open_transactions) < 2:
            return False

        # 检查价格趋势和加仓模式
        prices = [t.deal_avg_price for t in open_transactions if t.deal_avg_price > 0]
        if len(prices) < 2:
            return False

        # 价格下降时加仓（多头）或价格上升时加仓（空头）
        price_declining = sum(1 for i in range(1, len(prices)) if prices[i] < prices[i-1])

        return price_declining >= len(prices) * 0.6  # 60%以上价格下降时加仓

    def _identify_lifecycle_stages(self, cumulative_volumes: List[float]) -> List[dict]:
        """
        识别订单生命周期阶段

        参数:
            cumulative_volumes: 累计持仓序列

        返回:
            生命周期阶段列表
        """
        if not cumulative_volumes:
            return []

        stages = []
        current_stage = None
        stage_start = 0

        for i, volume in enumerate(cumulative_volumes):
            # 确定当前阶段类型
            if i == 0:
                stage_type = 'opening'
            elif volume > cumulative_volumes[i-1]:
                stage_type = 'accumulating'
            elif volume < cumulative_volumes[i-1]:
                stage_type = 'reducing'
            else:
                stage_type = 'holding'

            # 检查阶段变化
            if current_stage != stage_type:
                if current_stage is not None:
                    # 结束前一个阶段
                    stages.append({
                        'stage': current_stage,
                        'start_index': stage_start,
                        'end_index': i - 1,
                        'duration': i - stage_start,
                        'start_volume': cumulative_volumes[stage_start],
                        'end_volume': cumulative_volumes[i-1]
                    })

                # 开始新阶段
                current_stage = stage_type
                stage_start = i

        # 添加最后一个阶段
        if current_stage is not None:
            stages.append({
                'stage': current_stage,
                'start_index': stage_start,
                'end_index': len(cumulative_volumes) - 1,
                'duration': len(cumulative_volumes) - stage_start,
                'start_volume': cumulative_volumes[stage_start],
                'end_volume': cumulative_volumes[-1]
            })

        return stages

    def analyze_time_intervals(self, transactions: List) -> dict:
        """
        高级时间间隔分析

        分析交易的时间模式，识别高频交易、集中交易等行为

        参数:
            transactions: PositionTransaction列表

        返回:
            时间间隔分析结果
        """
        if len(transactions) < 2:
            return {'error': '交易数量不足，无法分析时间间隔'}

        # 计算所有时间间隔
        intervals = []
        same_minute_pairs = []
        same_second_pairs = []

        for i in range(1, len(transactions)):
            prev_time = transactions[i-1].timestamp
            curr_time = transactions[i].timestamp

            # 计算不同精度的时间间隔
            interval_seconds = (curr_time - prev_time).total_seconds()
            interval_minutes = interval_seconds / 60.0

            intervals.append({
                'index': i,
                'prev_time': prev_time,
                'curr_time': curr_time,
                'interval_seconds': interval_seconds,
                'interval_minutes': interval_minutes,
                'is_same_minute': self._is_same_minute(prev_time, curr_time),
                'is_same_second': interval_seconds < 1.0
            })

            # 统计同分钟和同秒交易
            if self._is_same_minute(prev_time, curr_time):
                same_minute_pairs.append(i)

            if interval_seconds < 1.0:
                same_second_pairs.append(i)

        # 统计分析
        interval_minutes_list = [iv['interval_minutes'] for iv in intervals]

        analysis = {
            'total_intervals': len(intervals),
            'same_minute_count': len(same_minute_pairs),
            'same_second_count': len(same_second_pairs),
            'same_minute_ratio': len(same_minute_pairs) / len(intervals) if intervals else 0,

            # 时间间隔统计
            'min_interval_minutes': min(interval_minutes_list) if interval_minutes_list else 0,
            'max_interval_minutes': max(interval_minutes_list) if interval_minutes_list else 0,
            'avg_interval_minutes': sum(interval_minutes_list) / len(interval_minutes_list) if interval_minutes_list else 0,
            'median_interval_minutes': self._calculate_median(interval_minutes_list),

            # 时间模式识别
            'time_patterns': self._identify_time_patterns(intervals),
            'trading_intensity': self._calculate_trading_intensity(intervals),
            'burst_periods': self._identify_burst_periods(intervals),

            # 详细间隔数据
            'interval_details': intervals,
            'same_minute_pairs': same_minute_pairs,
            'same_second_pairs': same_second_pairs
        }

        return analysis

    def _calculate_median(self, values: List[float]) -> float:
        """计算中位数"""
        if not values:
            return 0.0

        sorted_values = sorted(values)
        n = len(sorted_values)

        if n % 2 == 0:
            return (sorted_values[n//2 - 1] + sorted_values[n//2]) / 2
        else:
            return sorted_values[n//2]

    def _identify_time_patterns(self, intervals: List[dict]) -> dict:
        """
        识别时间模式

        参数:
            intervals: 时间间隔列表

        返回:
            时间模式分析
        """
        if not intervals:
            return {}

        # 按时间间隔分类
        instant_trades = sum(1 for iv in intervals if iv['interval_seconds'] < 1)  # <1秒
        rapid_trades = sum(1 for iv in intervals if 1 <= iv['interval_seconds'] < 10)  # 1-10秒
        quick_trades = sum(1 for iv in intervals if 10 <= iv['interval_seconds'] < 60)  # 10秒-1分钟
        normal_trades = sum(1 for iv in intervals if 60 <= iv['interval_seconds'] < 300)  # 1-5分钟
        slow_trades = sum(1 for iv in intervals if iv['interval_seconds'] >= 300)  # >5分钟

        total = len(intervals)

        return {
            'instant_trades': instant_trades,
            'rapid_trades': rapid_trades,
            'quick_trades': quick_trades,
            'normal_trades': normal_trades,
            'slow_trades': slow_trades,
            'instant_ratio': instant_trades / total if total > 0 else 0,
            'rapid_ratio': rapid_trades / total if total > 0 else 0,
            'quick_ratio': quick_trades / total if total > 0 else 0,
            'normal_ratio': normal_trades / total if total > 0 else 0,
            'slow_ratio': slow_trades / total if total > 0 else 0,
            'dominant_pattern': self._get_dominant_pattern(instant_trades, rapid_trades, quick_trades, normal_trades, slow_trades)
        }

    def _get_dominant_pattern(self, instant: int, rapid: int, quick: int, normal: int, slow: int) -> str:
        """获取主导的时间模式"""
        patterns = {
            'instant': instant,
            'rapid': rapid,
            'quick': quick,
            'normal': normal,
            'slow': slow
        }

        max_pattern = max(patterns, key=patterns.get)
        max_count = patterns[max_pattern]

        if max_count == 0:
            return 'unknown'

        return max_pattern

    def _calculate_trading_intensity(self, intervals: List[dict]) -> dict:
        """
        计算交易强度

        参数:
            intervals: 时间间隔列表

        返回:
            交易强度分析
        """
        if not intervals:
            return {'intensity': 0, 'level': 'none'}

        # 计算平均交易频率（每分钟交易次数）
        total_time_minutes = sum(iv['interval_minutes'] for iv in intervals)
        if total_time_minutes == 0:
            frequency = float('inf')  # 所有交易都在同一分钟内
        else:
            frequency = len(intervals) / total_time_minutes

        # 计算交易密度（基于时间间隔的倒数）
        density_scores = []
        for iv in intervals:
            if iv['interval_minutes'] > 0:
                density_scores.append(1 / iv['interval_minutes'])
            else:
                density_scores.append(100)  # 同分钟交易给高分

        avg_density = sum(density_scores) / len(density_scores) if density_scores else 0

        # 强度等级判断
        if frequency >= 10:  # 每分钟10次以上
            intensity_level = 'extreme'
        elif frequency >= 5:  # 每分钟5-10次
            intensity_level = 'very_high'
        elif frequency >= 2:  # 每分钟2-5次
            intensity_level = 'high'
        elif frequency >= 0.5:  # 每2分钟1次以上
            intensity_level = 'medium'
        elif frequency >= 0.1:  # 每10分钟1次以上
            intensity_level = 'low'
        else:
            intensity_level = 'very_low'

        return {
            'frequency_per_minute': frequency,
            'average_density': avg_density,
            'intensity_level': intensity_level,
            'total_time_minutes': total_time_minutes,
            'transaction_count': len(intervals) + 1  # +1因为intervals比transactions少1
        }

    def _identify_burst_periods(self, intervals: List[dict]) -> List[dict]:
        """
        识别爆发性交易期间

        爆发期定义：连续多笔交易在很短时间内完成

        参数:
            intervals: 时间间隔列表

        返回:
            爆发期列表
        """
        if len(intervals) < 3:
            return []

        burst_periods = []
        current_burst = None
        burst_threshold_minutes = 1.0  # 1分钟内的交易视为爆发
        min_burst_transactions = 3  # 至少3笔交易

        for i, interval in enumerate(intervals):
            if interval['interval_minutes'] <= burst_threshold_minutes:
                if current_burst is None:
                    # 开始新的爆发期
                    current_burst = {
                        'start_index': i,
                        'end_index': i + 1,
                        'start_time': interval['prev_time'],
                        'end_time': interval['curr_time'],
                        'transaction_count': 2,  # 包括前一笔和当前笔
                        'total_duration_minutes': interval['interval_minutes']
                    }
                else:
                    # 扩展当前爆发期
                    current_burst['end_index'] = i + 1
                    current_burst['end_time'] = interval['curr_time']
                    current_burst['transaction_count'] += 1
                    current_burst['total_duration_minutes'] += interval['interval_minutes']
            else:
                # 结束当前爆发期
                if current_burst and current_burst['transaction_count'] >= min_burst_transactions:
                    current_burst['intensity'] = current_burst['transaction_count'] / current_burst['total_duration_minutes']
                    burst_periods.append(current_burst)
                current_burst = None

        # 处理最后一个爆发期
        if current_burst and current_burst['transaction_count'] >= min_burst_transactions:
            current_burst['intensity'] = current_burst['transaction_count'] / current_burst['total_duration_minutes']
            burst_periods.append(current_burst)

        return burst_periods

    def _calculate_time_interval_optimized(self, prev_time: datetime, curr_time: datetime) -> float:
        """
        优化的时间间隔计算

        参数:
            prev_time: 前一笔交易时间
            curr_time: 当前交易时间

        返回:
            时间间隔（分钟）
        """
        if self._is_same_minute(prev_time, curr_time):
            return 0.0
        else:
            time_diff_seconds = (curr_time - prev_time).total_seconds()
            return max(0.0, time_diff_seconds / 60.0)  # 确保非负值

    def get_config_info(self) -> Dict[str, Any]:
        """
        获取第四代对敲检测器的配置信息

        返回:
            配置信息字典，包含所有参数说明
        """
        return {
            'version': '第四代PositionBasedOptimizer',
            'description': '基于完整订单生命周期的对敲检测器',
            'config': self.config.copy(),
            'features': [
                '统一时间窗口（同账户和跨账户都是15秒）',
                '简化金额匹配（统一5%相对容差）',
                '盈亏对冲分析（关键特征检测）',
                '完整订单生命周期分析',
                '高效的单次遍历算法'
            ],
            'advantages': [
                '消除了多套配置参数的混乱',
                '提高了检测精度和性能',
                '减少了误报率',
                '简化了维护工作'
            ]
        }
    
    def _build_indexes(self, position: CompletePosition):
        """构建索引以提升查询性能"""
        # 用户索引
        self.user_positions_map[position.member_id].append(position.position_id)
        
        # 合约索引
        self.contract_positions_map[position.contract_name].append(position.position_id)
        
        # 时间窗口索引（按小时）
        hour_key = position.first_open_time.strftime('%Y-%m-%d %H')
        self.time_window_index[hour_key].append(position.position_id)
    
    def optimized_wash_trading_detection(self) -> List[Dict]:
        """
        优化的对敲检测：基于完整订单的首笔开仓预筛选
        使用高效的单次遍历+二选一判断逻辑
        """
        logger.info("🚀 开始第四代对敲检测 (PositionBasedOptimizer)")
        logger.info("📊 配置参数: 时间窗口=15秒, 金额容差=5%, 盈亏对冲阈值=0.7, 对敲评分阈值=0.7")
        wash_trading_pairs = []

        # 🔍 调试信息：检查订单完整性分布
        total_positions = len(self.complete_positions)
        completed_positions = sum(1 for pos in self.complete_positions.values() if pos.is_completed)
        incomplete_positions = total_positions - completed_positions

        logger.info(f"📊 订单完整性统计: 总订单={total_positions}, 已完成={completed_positions}, 未完成={incomplete_positions}")

        # 第一步：首笔开仓预筛选
        # 按合约分组，只考虑完整订单的首次开仓
        contract_groups = defaultdict(list)
        for pos_id, position in self.complete_positions.items():
            if position.is_completed:  # 只检测已完成的订单
                contract_groups[position.contract_name].append(position)
        
        # 🔍 调试信息：检查各合约的订单分布
        total_valid_positions = sum(len(positions) for positions in contract_groups.values())
        logger.info(f"📊 有效检测订单分布: 总合约数={len(contract_groups)}, 总有效订单数={total_valid_positions}")

        # 显示前几个合约的详细信息
        for i, (contract_name, positions) in enumerate(list(contract_groups.items())[:3]):
            logger.info(f"📊 合约 {contract_name}: {len(positions)} 个已完成订单")

        for contract_name, positions in contract_groups.items():
            logger.debug(f"检测合约 {contract_name}，订单数: {len(positions)}")

            # 🔍 调试信息：检查订单方向分布
            long_count = sum(1 for pos in positions if pos.primary_side == 1)
            short_count = sum(1 for pos in positions if pos.primary_side == 3)
            logger.debug(f"合约 {contract_name} 方向分布: 多头={long_count}, 空头={short_count}")

            # 🚀 高效的单次遍历检测
            pairs_checked = 0
            side_filtered = 0
            time_filtered = 0
            amount_filtered = 0

            for i, pos_a in enumerate(positions):
                for pos_b in positions[i+1:]:
                    pairs_checked += 1

                    # 预筛选：方向必须相反才可能对敲
                    if pos_a.primary_side == pos_b.primary_side:
                        side_filtered += 1
                        continue

                    # 时间窗口检查
                    time_diff = abs((pos_a.first_open_time - pos_b.first_open_time).total_seconds())
                    if time_diff > self.config['wash_trading_time_window']:
                        time_filtered += 1
                        continue

                    # 金额匹配检查
                    if not self._amounts_match(pos_a.total_open_amount, pos_b.total_open_amount):
                        amount_filtered += 1
                        continue

                    # 🚀 关键：二选一判断逻辑
                    is_same_account = (pos_a.member_id == pos_b.member_id)

                    # 深度分析完整订单特征
                    pair_analysis = self._analyze_wash_trading_pair(pos_a, pos_b, is_same_account)

                    # 🔍 调试信息：显示评分详情
                    logger.info(f"配对分析 {pos_a.position_id} vs {pos_b.position_id}: "
                               f"wash_score={pair_analysis.get('wash_score', 0):.3f}, "
                               f"profit_hedge_score={pair_analysis.get('profit_hedge_score', 0):.3f}, "
                               f"is_wash_trading={pair_analysis.get('is_wash_trading', False)}")

                    if pair_analysis['is_wash_trading']:
                        wash_trading_pairs.append(pair_analysis)

            # 🔍 调试信息：显示过滤统计
            if pairs_checked > 0:
                logger.info(f"合约 {contract_name} 检测统计: 总配对={pairs_checked}, "
                           f"方向过滤={side_filtered}, 时间过滤={time_filtered}, 金额过滤={amount_filtered}")

        
        # 统计结果
        same_account_count = sum(1 for pair in wash_trading_pairs if pair.get('is_same_account'))
        cross_account_count = len(wash_trading_pairs) - same_account_count
        
        logger.info(f"对敲检测完成，发现 {len(wash_trading_pairs)} 对可疑交易 "
                   f"(同账户: {same_account_count}, 跨账户: {cross_account_count})")
        return wash_trading_pairs
    
    def _amounts_match(self, amount_a: float, amount_b: float) -> bool:
        """检查金额是否匹配"""
        if amount_a == 0 or amount_b == 0:
            return False
        
        diff_ratio = abs(amount_a - amount_b) / max(amount_a, amount_b)
        return diff_ratio <= self.config['amount_match_tolerance']
    
    def _analyze_wash_trading_pair(self, pos_a: CompletePosition, pos_b: CompletePosition, is_same_account: bool) -> Dict:
        """深度分析对敲交易对的完整特征"""
        
        # 盈亏对冲分析（关键特征！）
        # 确保数据类型一致性
        profit_a = float(pos_a.real_profit) if pos_a.real_profit is not None else 0.0
        profit_b = float(pos_b.real_profit) if pos_b.real_profit is not None else 0.0

        total_profit = profit_a + profit_b
        profit_sum = abs(profit_a) + abs(profit_b)

        if profit_sum > 0:
            profit_hedge_score = 1.0 - abs(total_profit) / profit_sum
        else:
            profit_hedge_score = 0.0
        
        # 时间模式分析
        time_diff = abs((pos_a.first_open_time - pos_b.first_open_time).total_seconds())
        time_match_score = 1.0 - min(time_diff / self.config['wash_trading_time_window'], 1.0)
        
        # 金额匹配度
        # 确保数据类型一致性
        amount_a = float(pos_a.total_open_amount) if pos_a.total_open_amount is not None else 0.0
        amount_b = float(pos_b.total_open_amount) if pos_b.total_open_amount is not None else 0.0

        amount_diff = abs(amount_a - amount_b)
        max_amount = max(amount_a, amount_b)
        amount_match_score = 1.0 - (amount_diff / max_amount) if max_amount > 0 else 0.0

        # 持仓时长相似性
        duration_a = float(pos_a.total_duration_minutes) if pos_a.total_duration_minutes is not None else 0.0
        duration_b = float(pos_b.total_duration_minutes) if pos_b.total_duration_minutes is not None else 0.0

        duration_diff = abs(duration_a - duration_b)
        max_duration = max(duration_a, duration_b, 1.0)
        duration_similarity = 1.0 - min(duration_diff / max_duration, 1.0)
        
        # 综合评分
        wash_score = (time_match_score * 0.3 + 
                     amount_match_score * 0.3 + 
                     profit_hedge_score * 0.3 + 
                     duration_similarity * 0.1)
        
        is_wash_trading = (wash_score > self.config['wash_score_threshold'] and 
                          profit_hedge_score > self.config['profit_hedge_threshold'])
        
        # 🚀 新增：明确的检测方法标记
        detection_method = 'same_account_wash_trading' if is_same_account else 'cross_account_wash_trading'
        
        # 构建详细交易对信息（用于详细存储）
        trade_pair_detail = self._build_trade_pair_detail(pos_a, pos_b, time_diff)
        
        # 🚀 统一的结果格式，确保与存储管理器兼容
        result = {
            'is_wash_trading': is_wash_trading,
            'detection_type': 'wash_trading',
            'detection_method': detection_method,  # 🚀 关键：明确标记同账户或跨账户
            
            # 用户信息 - 兼容存储管理器期望的字段名
            'member_id': pos_a.member_id,  # 主用户ID
            'user_id': pos_a.member_id,    # 兼容字段
            'opponent_member_id': pos_b.member_id if not is_same_account else pos_a.member_id,
            'counterparty_ids': [pos_b.member_id] if not is_same_account else [],
            
            # 用户详细信息
            'user_a': {
                'member_id': pos_a.member_id,
                'position_id': pos_a.position_id,
                'side': pos_a.primary_side,
                'amount': pos_a.total_open_amount,
                'real_profit': pos_a.real_profit,
                'duration_minutes': pos_a.total_duration_minutes,
                'open_time': pos_a.first_open_time.isoformat() if pos_a.first_open_time else None,
                'close_time': pos_a.last_close_time.isoformat() if pos_a.last_close_time else None
            },
            'user_b': {
                'member_id': pos_b.member_id,
                'position_id': pos_b.position_id,
                'side': pos_b.primary_side,
                'amount': pos_b.total_open_amount,
                'real_profit': pos_b.real_profit,
                'duration_minutes': pos_b.total_duration_minutes,
                'open_time': pos_b.first_open_time.isoformat() if pos_b.first_open_time else None,
                'close_time': pos_b.last_close_time.isoformat() if pos_b.last_close_time else None
            },
            
            # 合约和风险信息
            'contract_name': pos_a.contract_name,
            'wash_score': wash_score,
            'profit_hedge_score': profit_hedge_score,
            'time_match_score': time_match_score,
            'amount_match_score': amount_match_score,
            'duration_similarity': duration_similarity,
            'total_profit': total_profit,
            'time_diff_seconds': time_diff,
            'abnormal_volume': pos_a.total_open_amount + pos_b.total_open_amount,
            'severity': 'High' if wash_score > 0.8 else 'Medium',
            'reason': f'盈亏对冲评分: {profit_hedge_score:.2f}, 综合评分: {wash_score:.2f}',
            'first_open_time': min(pos_a.first_open_time, pos_b.first_open_time),
            
            # 详细交易对信息
            'trade_pair_detail': trade_pair_detail,
            
            # 🚀 新增：账户类型标记（便于后续处理）
            'account_type': 'same_account' if is_same_account else 'cross_account',
            'is_same_account': is_same_account
        }
        
        return result
    
    def _build_trade_pair_detail(self, pos_a: CompletePosition, pos_b: CompletePosition, time_diff: float) -> Dict:
        """构建详细的交易对信息，用于详细存储"""
        
        # 计算平仓时间差
        close_time_gap = 0
        if pos_a.last_close_time and pos_b.last_close_time:
            close_time_gap = abs((pos_a.last_close_time - pos_b.last_close_time).total_seconds())
        
        # 构建符合WashTradingStorageManager期望格式的详细信息
        trade_pair_detail = {
            'pair_index': 1,  # 单个交易对
            'contract_name': pos_a.contract_name,
            'user_a': {
                'member_id': pos_a.member_id,
                'position_id': pos_a.position_id,
                'open_time': pos_a.first_open_time.isoformat() if pos_a.first_open_time else None,
                'open_side': pos_a.primary_side,
                'open_amount': pos_a.total_open_amount,
                'close_time': pos_a.last_close_time.isoformat() if pos_a.last_close_time else None,
                'close_side': 4 if pos_a.primary_side == 1 else 2,  # 1开多->4平多, 3开空->2平空
                'close_amount': pos_a.total_close_amount,
                'profit': pos_a.real_profit
            },
            'user_b': {
                'member_id': pos_b.member_id,
                'position_id': pos_b.position_id,
                'open_time': pos_b.first_open_time.isoformat() if pos_b.first_open_time else None,
                'open_side': pos_b.primary_side,
                'open_amount': pos_b.total_open_amount,
                'close_time': pos_b.last_close_time.isoformat() if pos_b.last_close_time else None,
                'close_side': 4 if pos_b.primary_side == 1 else 2,  # 1开多->4平多, 3开空->2平空
                'close_amount': pos_b.total_close_amount,
                'profit': pos_b.real_profit
            },
            'time_gaps': {
                'open_gap_seconds': int(time_diff),
                'close_gap_seconds': int(close_time_gap)
            },
            'total_amount': pos_a.total_open_amount + pos_b.total_open_amount,
            'net_profit': pos_a.real_profit + pos_b.real_profit,
            'risk_level': 'High' if self._calculate_wash_score(pos_a, pos_b) > 0.8 else 'Medium',
            'risk_score': self._calculate_wash_score(pos_a, pos_b)
        }
        
        return trade_pair_detail
    
    def _calculate_wash_score(self, pos_a: CompletePosition, pos_b: CompletePosition) -> float:
        """计算对敲评分（简化版本，用于风险等级评估）"""
        # 简化的评分计算，复用现有逻辑
        time_diff = abs((pos_a.first_open_time - pos_b.first_open_time).total_seconds())
        time_match_score = 1.0 - min(time_diff / self.config['wash_trading_time_window'], 1.0)
        
        # 确保数据类型一致性
        amount_a = float(pos_a.total_open_amount) if pos_a.total_open_amount is not None else 0.0
        amount_b = float(pos_b.total_open_amount) if pos_b.total_open_amount is not None else 0.0

        amount_diff = abs(amount_a - amount_b)
        max_amount = max(amount_a, amount_b)
        amount_match_score = 1.0 - (amount_diff / max_amount) if max_amount > 0 else 0.0
        
        return (time_match_score + amount_match_score) / 2
    
    def optimized_high_frequency_detection(self) -> List[Dict]:
        """
        优化的高频检测：基于完整订单，过滤正常加仓
        """
        logger.info("开始优化高频检测")
        high_freq_results = []
        
        # 按用户+合约分组
        user_contract_groups = defaultdict(list)
        for position in self.complete_positions.values():
            key = f"{position.member_id}_{position.contract_name}"
            user_contract_groups[key].append(position)
        
        for key, positions in user_contract_groups.items():
            member_id, contract_name = key.split('_', 1)
            
            # 按小时分组统计完整订单频率
            hourly_positions = defaultdict(list)
            for position in positions:
                hour_key = position.first_open_time.strftime('%Y-%m-%d %H')
                hourly_positions[hour_key].append(position)
            
            # 检测异常高频
            for hour_key, hour_positions in hourly_positions.items():
                if len(hour_positions) >= self.config['high_frequency_threshold']:
                    
                    # 关键：过滤正常的分批建仓（加仓次数多的订单）
                    abnormal_positions = [
                        pos for pos in hour_positions 
                        if pos.add_position_count <= 2  # 加仓次数少，可能是异常
                    ]
                    
                    # 计算快进快出比例
                    quick_trades = [pos for pos in hour_positions if pos.is_quick_trade]
                    quick_trades_ratio = len(quick_trades) / len(hour_positions)
                    
                    if len(abnormal_positions) >= self.config['high_frequency_threshold'] * 0.7:
                        high_freq_results.append({
                            'detection_type': 'high_frequency',
                            'member_id': member_id,
                            'contract_name': contract_name,
                            'hour': hour_key,
                            'total_positions': len(hour_positions),
                            'abnormal_positions': len(abnormal_positions),
                            'quick_trades_count': len(quick_trades),
                            'quick_trades_ratio': quick_trades_ratio,
                            'abnormal_volume': sum(pos.total_open_amount for pos in abnormal_positions),
                            'total_volume': sum(pos.total_open_amount for pos in hour_positions),
                            'severity': 'High' if quick_trades_ratio > 0.8 else 'Medium',
                            'reason': f'小时内异常订单 {len(abnormal_positions)} 笔，快进快出比例 {quick_trades_ratio:.1%}',
                            'detection_method': 'complete_position_frequency'
                        })
        
        logger.info(f"高频检测完成，发现 {len(high_freq_results)} 个异常用户")
        return high_freq_results
    
    def optimized_arbitrage_detection(self) -> List[Dict]:
        """优化的套利检测：仅使用基于时间模式的资金费率套利检测"""
        logger.info("开始优化套利检测")
        arbitrage_results = []
        
        funding_arbitrage_results = self._detect_funding_rate_arbitrage()
        arbitrage_results.extend(funding_arbitrage_results)
        
        logger.info(f"资金费率套利检测完成，发现 {len(arbitrage_results)} 个套利用户")
        return arbitrage_results
    

    
    def _detect_funding_rate_arbitrage(self) -> List[Dict]:
        """基于时间模式的资金费率套利检测"""
        from ..algorithms.funding_rate_arbitrage_realistic import RealisticFundingArbitrageDetector
        
        # 将完整订单转换为DataFrame格式供检测器使用
        position_data = []
        for position in self.complete_positions.values():
            # 开仓记录
            position_data.append({
                'position_id': position.position_id,
                'member_id': position.member_id,
                'contract_name': position.contract_name,
                'side': position.primary_side,
                'timestamp': position.first_open_time,
                'deal_vol_usdt': position.total_open_amount
            })
            
            # 平仓记录（如果有）
            if position.is_completed and position.first_close_time:
                close_side = 4 if position.primary_side == 1 else 2  # 1->4(平多), 3->2(平空)
                position_data.append({
                    'position_id': position.position_id,
                    'member_id': position.member_id,
                    'contract_name': position.contract_name,
                    'side': close_side,
                    'timestamp': position.first_close_time,
                    'deal_vol_usdt': position.total_close_amount
                })
        
        if not position_data:
            return []
        
        # 创建DataFrame
        df = pd.DataFrame(position_data)
        
        # 使用资金费率套利检测器
        detector = RealisticFundingArbitrageDetector()
        funding_results = detector.detect(df)
        
        # 转换结果格式以保持一致性
        converted_results = []
        for result in funding_results:
            converted_results.append({
                'detection_type': 'funding_arbitrage',  # 统一类型名称
                'member_id': result['member_id'],
                'contract_name': result['contract_name'],
                'total_profit': 0.0,  # 资金费率套利不关注盈亏
                'total_positions': result['total_positions'],
                'pattern_matched_positions': result['pattern_matched_positions'],
                'pattern_ratio': result['pattern_ratio'],
                'contracts_involved': result['contracts_involved'],
                'funding_cycle_distribution': result['funding_cycle_distribution'],
                'abnormal_volume': result['abnormal_volume'],
                'severity': result['severity'],
                'reason': result['reason'],
                'detection_method': result['detection_method'],
                'pattern_analysis': result['pattern_analysis']
            })
        
        return converted_results
    
    def get_comprehensive_analysis(self) -> Dict[str, Any]:
        """获取综合分析结果"""
        # 执行所有检测
        wash_results = self.optimized_wash_trading_detection()
        high_freq_results = self.optimized_high_frequency_detection()
        arbitrage_results = self.optimized_arbitrage_detection()
        
        # 合并所有结果
        all_results = wash_results + high_freq_results + arbitrage_results
        
        # 🚀 新增：对敲结果详细分类统计
        same_account_wash_results = [r for r in wash_results if r.get('account_type') == 'same_account']
        cross_account_wash_results = [r for r in wash_results if r.get('account_type') == 'cross_account']
        
        # 统计信息
        stats = {
            'total_positions': len(self.complete_positions),
            'completed_positions': sum(1 for pos in self.complete_positions.values() if pos.is_completed),
            'quick_trades': sum(1 for pos in self.complete_positions.values() if pos.is_quick_trade),
            'total_real_profit': sum(pos.real_profit for pos in self.complete_positions.values()),
            'wash_trading_pairs': len(wash_results),
            'same_account_wash_pairs': len(same_account_wash_results),  # 🚀 新增
            'cross_account_wash_pairs': len(cross_account_wash_results),  # 🚀 新增
            'high_frequency_users': len(high_freq_results),
            'arbitrage_users': len(arbitrage_results),
            'total_abnormal_volume': sum(result.get('abnormal_volume', 0) for result in all_results)
        }
        
        return {
            'results': all_results,
            'statistics': stats,
            'detection_summary': {
                'wash_trading': len(wash_results),
                'same_account_wash_trading': len(same_account_wash_results),  # 🚀 新增
                'cross_account_wash_trading': len(cross_account_wash_results),  # 🚀 新增
                'high_frequency': len(high_freq_results),
                'arbitrage': len(arbitrage_results),
                'total_detections': len(all_results)
            },
            # 🚀 新增：详细的对敲分析结果
            'wash_trading_details': {
                'same_account_pairs': same_account_wash_results,
                'cross_account_pairs': cross_account_wash_results,
                'same_account_summary': {
                    'total_pairs': len(same_account_wash_results),
                    'high_risk_pairs': len([r for r in same_account_wash_results if r.get('severity') == 'High']),
                    'medium_risk_pairs': len([r for r in same_account_wash_results if r.get('severity') == 'Medium']),
                    'total_volume': sum(r.get('abnormal_volume', 0) for r in same_account_wash_results)
                },
                'cross_account_summary': {
                    'total_pairs': len(cross_account_wash_results),
                    'high_risk_pairs': len([r for r in cross_account_wash_results if r.get('severity') == 'High']),
                    'medium_risk_pairs': len([r for r in cross_account_wash_results if r.get('severity') == 'Medium']),
                    'total_volume': sum(r.get('abnormal_volume', 0) for r in cross_account_wash_results)
                }
            }
        }
    
    def clear_cache(self):
        """清理缓存"""
        self.complete_positions.clear()
        self.user_positions_map.clear()
        self.contract_positions_map.clear()
        self.time_window_index.clear()
