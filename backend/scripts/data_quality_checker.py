#!/usr/bin/env python3
"""
数据质量检查和清理工具
用于识别和处理系统中的模拟数据，提升数据真实性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.duckdb_manager import db_manager
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self):
        self.quality_report = {
            'total_records': 0,
            'real_data_records': 0,
            'simulated_data_records': 0,
            'invalid_data_records': 0,
            'quality_issues': [],
            'recommendations': []
        }
    
    def check_contract_data_quality(self) -> Dict[str, Any]:
        """检查合约数据质量"""
        logger.info("========== 开始检查合约数据质量 ==========")
        
        try:
            # 检查是否存在合约数据表
            tables = db_manager.execute_sql("SHOW TABLES")
            table_names = [table[0] for table in tables] if tables else []
            
            if 'position_analysis' not in table_names:
                logger.warning("未找到position_analysis表，可能需要先运行合约分析")
                return self.quality_report
            
            # 获取所有持仓数据
            position_data = db_manager.execute_sql("""
                SELECT 
                    position_id, member_id, contract_name, 
                    total_pnl, total_commission, net_pnl,
                    open_time, close_time, duration_minutes,
                    total_open_amount, total_close_amount
                FROM position_analysis
                ORDER BY open_time DESC
            """)
            
            if not position_data:
                logger.warning("position_analysis表中没有数据")
                return self.quality_report
            
            logger.info(f"获取到 {len(position_data)} 条持仓数据")
            self.quality_report['total_records'] = len(position_data)
            
            # 转换为DataFrame便于分析
            df = pd.DataFrame(position_data, columns=[
                'position_id', 'member_id', 'contract_name', 
                'total_pnl', 'total_commission', 'net_pnl',
                'open_time', 'close_time', 'duration_minutes',
                'total_open_amount', 'total_close_amount'
            ])
            
            # 1. 检查模拟数据特征
            self._check_simulated_data_patterns(df)
            
            # 2. 检查数据完整性
            self._check_data_completeness(df)
            
            # 3. 检查数据逻辑一致性
            self._check_data_consistency(df)
            
            # 4. 检查异常值
            self._check_outliers(df)
            
            # 5. 生成质量报告
            self._generate_quality_recommendations()
            
            logger.info("========== 合约数据质量检查完成 ==========")
            return self.quality_report
            
        except Exception as e:
            logger.error(f"数据质量检查失败: {str(e)}")
            self.quality_report['quality_issues'].append(f"检查过程出错: {str(e)}")
            return self.quality_report
    
    def _check_simulated_data_patterns(self, df: pd.DataFrame):
        """检查模拟数据模式"""
        logger.info("检查模拟数据模式...")
        
        # 1. 检查profit字段是否为成交量的固定比例（模拟数据特征）
        df['pnl_ratio'] = df['total_pnl'] / df['total_open_amount']
        
        # 统计盈亏比例分布
        ratio_counts = df['pnl_ratio'].value_counts()
        
        # 如果某个固定比例（如0.001）出现频率过高，可能是模拟数据
        for ratio, count in ratio_counts.items():
            if abs(ratio - 0.001) < 0.0001 and count > len(df) * 0.1:  # 超过10%的数据都是0.1%比例
                self.quality_report['quality_issues'].append(
                    f"发现疑似模拟数据：{count} 条记录的盈亏比例为 {ratio:.4f}，可能是模拟生成"
                )
                self.quality_report['simulated_data_records'] += count
        
        # 2. 检查手续费是否为0（可能是模拟数据）
        zero_commission_count = (df['total_commission'] == 0).sum()
        if zero_commission_count > 0:
            self.quality_report['quality_issues'].append(
                f"发现 {zero_commission_count} 条记录的手续费为0，可能缺少真实手续费数据"
            )
        
        # 3. 检查position_id格式（模拟数据通常有特定格式）
        simulated_position_count = 0
        for pos_id in df['position_id']:
            if str(pos_id).startswith('pos_') and '_a' in str(pos_id):
                simulated_position_count += 1
        
        if simulated_position_count > 0:
            self.quality_report['quality_issues'].append(
                f"发现 {simulated_position_count} 条记录使用模拟position_id格式（pos_xxx_a）"
            )
            self.quality_report['simulated_data_records'] += simulated_position_count
        
        # 计算真实数据比例
        total_simulated = self.quality_report['simulated_data_records']
        self.quality_report['real_data_records'] = len(df) - total_simulated
        
        logger.info(f"模拟数据检查完成 - 真实数据: {self.quality_report['real_data_records']}, "
                   f"模拟数据: {total_simulated}")
    
    def _check_data_completeness(self, df: pd.DataFrame):
        """检查数据完整性"""
        logger.info("检查数据完整性...")
        
        # 检查必要字段的缺失情况
        required_fields = ['position_id', 'member_id', 'contract_name', 'open_time']
        
        for field in required_fields:
            null_count = df[field].isnull().sum()
            if null_count > 0:
                self.quality_report['quality_issues'].append(
                    f"字段 {field} 有 {null_count} 条空值记录"
                )
                self.quality_report['invalid_data_records'] += null_count
        
        # 检查close_time为空的情况（未平仓）
        unclosed_positions = df['close_time'].isnull().sum()
        if unclosed_positions > 0:
            logger.info(f"发现 {unclosed_positions} 个未平仓的持仓记录")
        
        # 检查时间字段的有效性 (指定格式避免警告)
        try:
            df['open_time'] = pd.to_datetime(
                df['open_time'],
                format='%Y/%m/%d %H:%M:%S',
                errors='coerce'
            )
            invalid_open_time = df['open_time'].isnull().sum()
            if invalid_open_time > 0:
                self.quality_report['quality_issues'].append(
                    f"开仓时间无效的记录: {invalid_open_time} 条"
                )
        except Exception as e:
            self.quality_report['quality_issues'].append(f"开仓时间格式错误: {str(e)}")
    
    def _check_data_consistency(self, df: pd.DataFrame):
        """检查数据逻辑一致性"""
        logger.info("检查数据逻辑一致性...")
        
        # 1. 检查净盈亏 = 总盈亏 - 手续费
        df['calculated_net_pnl'] = df['total_pnl'] - df['total_commission']
        pnl_inconsistent = abs(df['net_pnl'] - df['calculated_net_pnl']) > 0.01
        inconsistent_count = pnl_inconsistent.sum()
        
        if inconsistent_count > 0:
            self.quality_report['quality_issues'].append(
                f"净盈亏计算不一致的记录: {inconsistent_count} 条"
            )
        
        # 2. 检查持仓时长的合理性
        negative_duration = (df['duration_minutes'] < 0).sum()
        if negative_duration > 0:
            self.quality_report['quality_issues'].append(
                f"持仓时长为负数的记录: {negative_duration} 条"
            )
        
        # 3. 检查交易金额的合理性
        zero_amount = (df['total_open_amount'] <= 0).sum()
        if zero_amount > 0:
            self.quality_report['quality_issues'].append(
                f"开仓金额为0或负数的记录: {zero_amount} 条"
            )
    
    def _check_outliers(self, df: pd.DataFrame):
        """检查异常值"""
        logger.info("检查数据异常值...")
        
        # 使用IQR方法检查异常值
        numeric_columns = ['total_pnl', 'total_commission', 'duration_minutes', 
                          'total_open_amount', 'total_close_amount']
        
        for col in numeric_columns:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                if outliers > 0:
                    logger.info(f"字段 {col} 发现 {outliers} 个异常值")
    
    def _generate_quality_recommendations(self):
        """生成质量改进建议"""
        recommendations = []
        
        # 基于发现的问题生成建议
        simulated_ratio = self.quality_report['simulated_data_records'] / max(self.quality_report['total_records'], 1)
        
        if simulated_ratio > 0.1:
            recommendations.append(
                f"模拟数据比例过高（{simulated_ratio:.1%}），建议：\n"
                "  1. 检查数据源，确保使用真实交易数据\n"
                "  2. 修复profit字段生成逻辑，避免使用固定比例计算\n"
                "  3. 补充真实的手续费数据"
            )
        
        if self.quality_report['invalid_data_records'] > 0:
            recommendations.append(
                "发现数据完整性问题，建议：\n"
                "  1. 在数据导入时增加必要字段验证\n"
                "  2. 对缺失数据进行合理的默认值填充\n"
                "  3. 建立数据质量监控机制"
            )
        
        if len(self.quality_report['quality_issues']) > 5:
            recommendations.append(
                "数据质量问题较多，建议：\n"
                "  1. 建立数据清洗流程\n"
                "  2. 实施数据质量评分机制\n"
                "  3. 定期进行数据质量审核"
            )
        
        self.quality_report['recommendations'] = recommendations
    
    def clean_simulated_data(self) -> Dict[str, int]:
        """严格模式：报告模拟数据问题，不执行清理"""
        logger.info("========== 模拟数据问题报告（严格模式）==========")
        
        cleanup_stats = {
            'deleted_simulated_positions': 0,
            'updated_profit_fields': 0,
            'fixed_commission_data': 0
        }
        
        try:
            # 严格模式：不删除任何数据，只报告问题
            logger.error("严格模式：不执行任何数据清理操作")
            logger.error("发现的模拟数据问题需要从数据源层面解决：")
            
            # 1. 报告模拟position记录
            simulated_positions = db_manager.execute_sql("""
                SELECT COUNT(*) as count FROM position_analysis 
                WHERE position_id LIKE 'pos_%_a' OR position_id LIKE 'pos_%_b'
            """)
            
            if simulated_positions and simulated_positions[0][0] > 0:
                logger.error(f"   1. 模拟position_id记录: {simulated_positions[0][0]} 条")
                logger.error("      - 问题：使用模拟格式的仓位ID")
                logger.error("      - 建议：从交易系统获取真实仓位ID")
            
            # 2. 报告可能的模拟profit数据
            suspicious_profit_count = db_manager.execute_sql("""
                SELECT COUNT(*) as count FROM position_analysis 
                WHERE ABS(total_pnl / total_open_amount - 0.001) < 0.0001
            """)
            
            if suspicious_profit_count and suspicious_profit_count[0][0] > 0:
                logger.error(f"   2. 疑似模拟profit数据: {suspicious_profit_count[0][0]} 条")
                logger.error("      - 问题：盈亏数据使用固定比例计算")
                logger.error("      - 建议：从交易系统获取真实盈亏数据")
            
            # 3. 报告零手续费记录
            zero_commission_count = db_manager.execute_sql("""
                SELECT COUNT(*) as count FROM position_analysis 
                WHERE total_commission = 0 AND total_open_amount > 0
            """)
            
            if zero_commission_count and zero_commission_count[0][0] > 0:
                logger.error(f"   3. 零手续费记录: {zero_commission_count[0][0]} 条")
                logger.error("      - 问题：缺少真实手续费数据")
                logger.error("      - 建议：从交易系统获取真实手续费数据")
            
            # 严格模式：不执行任何清理操作
            logger.error("❌ 严格模式：不执行数据修复或删除操作")
            logger.error("💡 建议：")
            logger.error("   1. 联系数据团队补充真实数据源")
            logger.error("   2. 修改数据导入流程，避免生成模拟数据")
            logger.error("   3. 建立数据质量验证机制")
            
            logger.info("========== 模拟数据问题报告完成 ==========")
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"模拟数据问题报告失败: {str(e)}")
            return cleanup_stats
    
    def generate_quality_report(self) -> str:
        """生成质量报告"""
        report = f"""
========== 数据质量报告 ==========
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

数据概况:
- 总记录数: {self.quality_report['total_records']}
- 真实数据记录: {self.quality_report['real_data_records']}
- 模拟数据记录: {self.quality_report['simulated_data_records']}
- 无效数据记录: {self.quality_report['invalid_data_records']}

数据质量评分: {self._calculate_quality_score():.1f}/100

发现的问题:
"""
        
        for i, issue in enumerate(self.quality_report['quality_issues'], 1):
            report += f"{i}. {issue}\n"
        
        if self.quality_report['recommendations']:
            report += "\n改进建议:\n"
            for i, rec in enumerate(self.quality_report['recommendations'], 1):
                report += f"{i}. {rec}\n"
        
        report += "=================================="
        return report
    
    def _calculate_quality_score(self) -> float:
        """计算数据质量评分"""
        if self.quality_report['total_records'] == 0:
            return 0.0
        
        # 基础分数
        base_score = 100.0
        
        # 模拟数据扣分
        simulated_ratio = self.quality_report['simulated_data_records'] / self.quality_report['total_records']
        base_score -= simulated_ratio * 40  # 模拟数据每1%扣0.4分
        
        # 无效数据扣分
        invalid_ratio = self.quality_report['invalid_data_records'] / self.quality_report['total_records']
        base_score -= invalid_ratio * 30  # 无效数据每1%扣0.3分
        
        # 质量问题扣分
        base_score -= len(self.quality_report['quality_issues']) * 2  # 每个问题扣2分
        
        return max(base_score, 0.0)


def main():
    """主函数"""
    print("数据质量检查和清理工具")
    print("1. 检查数据质量")
    print("2. 清理模拟数据")
    print("3. 生成质量报告")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    checker = DataQualityChecker()
    
    if choice == '1':
        # 检查数据质量
        quality_report = checker.check_contract_data_quality()
        print("\n" + checker.generate_quality_report())
        
    elif choice == '2':
        # 清理模拟数据
        confirm = input("确认要清理模拟数据吗？这将删除一些记录 (y/N): ").strip().lower()
        if confirm == 'y':
            cleanup_stats = checker.clean_simulated_data()
            print(f"清理完成: {cleanup_stats}")
        else:
            print("操作已取消")
            
    elif choice == '3':
        # 生成质量报告
        quality_report = checker.check_contract_data_quality()
        report = checker.generate_quality_report()
        
        # 保存报告到文件
        report_file = f"data_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"质量报告已保存到: {report_file}")
        print("\n" + report)
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main() 