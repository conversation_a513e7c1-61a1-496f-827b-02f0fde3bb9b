2025-07-16 00:27:22,020 - modules.auth.utils.error_handler - ERROR - error_handler.py:39 - handle_redis_error - Redis错误 - 操作: test_operation, 错误: 测试Redis连接错误2025-07-18 02:08:44,795 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒2025-07-21 09:40:27,545 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-21 09:40:27,545 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-21 09:40:58,837 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-07-21 09:41:14,274 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-21 09:41:14,875 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: pheud4Cw..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-21 09:41:14,875 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 admin 生成2FA临时token
2025-07-21 09:41:38,369 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=pheud4Cw...
2025-07-21 09:41:40,069 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-21 09:41:40,073 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: pheud4Cw...
2025-07-21 09:41:40,743 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=Af6RDYCB75zOq5vQc6UNc_Z92GYUfmhXYdUTVRsZYe8
2025-07-21 09:41:40,744 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 14:38:55,844 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 14:38:55,844 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 14:40:04,301 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-22 14:40:05,226 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: oJba9jd0..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-22 14:40:40,635 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=oJba9jd0...
2025-07-22 14:40:40,971 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=oJba9jd0...
2025-07-22 14:40:40,977 - modules.auth.services.totp_service - INFO - totp_service.py:204 - verify_and_enable_2fa - 用户 1 完成2FA首次验证
2025-07-22 14:40:40,978 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: oJba9jd0...
2025-07-22 14:40:40,979 - modules.auth.services.totp_service - ERROR - totp_service.py:209 - verify_and_enable_2fa - 验证并启用2FA失败: TransactionContext Error: Conflict on update!
2025-07-22 14:40:41,832 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=6HvXZ3EPEBvdDvGeM4-_DjckyaYyaI34jMj9b_O9ePQ
2025-07-22 14:40:41,832 - modules.auth.api.twofa_api - INFO - twofa_api.py:149 - setup_verify_2fa - 用户2FA设置成功并完成登录: admin
2025-07-22 14:40:56,165 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 new_user
2025-07-22 14:41:02,810 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 test_user2
2025-07-22 14:43:26,209 - modules.auth.services.totp_service - INFO - totp_service.py:172 - setup_2fa_for_user - 为用户 reveen 设置2FA
2025-07-22 14:43:26,209 - modules.auth.services.auth_service - INFO - auth_service.py:141 - create_user - 创建用户成功: reveen, 角色: admin, 已强制设置2FA
2025-07-22 15:23:31,268 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:23:31,268 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:23:57,548 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:23:57,548 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:26:30,182 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:26:30,182 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:27:00,416 - modules.auth.services.session_manager - INFO - session_manager.py:79 - destroy_session - 销毁会话: 6HvXZ3EPEBvdDvGeM4-_DjckyaYyaI34jMj9b_O9ePQ
2025-07-22 15:27:01,107 - modules.auth.api.auth_api - INFO - auth_api.py:295 - logout - 用户登出: admin, IP: 127.0.0.1
2025-07-22 15:33:39,101 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:33:39,101 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:33:48,657 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:33:48,657 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:34:41,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:34:41,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:38:47,240 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:38:47,240 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:41:16,470 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: reveen, IP: 127.0.0.1
2025-07-22 15:41:17,131 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: TH868waq..., 用户ID: 2, 过期时间: 300秒, 结果: 成功
2025-07-22 15:43:28,951 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:43:28,951 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:45:59,747 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:45:59,747 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:01:11,397 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:01:11,397 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:26,765 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:04:26,766 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:31,848 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:04:31,849 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:44,791 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: reveen, IP: 127.0.0.1
2025-07-22 16:04:45,126 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: beEjnXCA..., 用户ID: 2, 过期时间: 300秒, 结果: 成功
2025-07-22 16:04:54,597 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-22 16:04:54,803 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: Qi7XsPec..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-22 16:04:54,803 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 admin 生成2FA临时token
2025-07-22 16:05:14,373 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=Qi7XsPec...
2025-07-22 16:05:14,688 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=Qi7XsPec...
2025-07-22 16:05:14,739 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-22 16:05:14,739 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: Qi7XsPec...
2025-07-22 16:05:14,922 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-22 16:05:14,923 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:150 - invalidate_temp_token - 临时token失效失败: Qi7XsPec...
2025-07-22 16:05:14,924 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=a2j39K5OF6lEQGbgfONWflOHjkQFmUx2nT16B1qPJ5o
2025-07-22 16:05:14,924 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 16:05:15,038 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=UkpD6qKZeETrOws164MxWvB_ZXxWJnW-q3YIP4MOo7s
2025-07-22 16:05:15,039 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 16:05:50,941 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 retry_user_4
2025-07-22 16:05:54,569 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 retry_user_0
2025-07-22 16:05:58,320 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 totp_test_user
2025-07-23 09:20:02,672 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-23 09:20:02,672 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-23 09:20:09,223 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin, IP: 127.0.0.1, 原因: 用户不存在
2025-07-23 09:20:24,019 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-07-23 09:20:24,053 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: vo84nUcV..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-07-23 09:20:45,042 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=vo84nUcV...
2025-07-23 09:20:45,061 - modules.auth.services.totp_service - INFO - totp_service.py:215 - verify_and_enable_2fa - 用户 817184 完成2FA首次验证
2025-07-23 09:20:45,066 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: vo84nUcV...
2025-07-23 09:20:45,090 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=xE2PtInnXeCzz2TiJUq1-r4JQaBu9BJdax9Vzbqaxus
2025-07-23 09:20:45,090 - modules.auth.api.twofa_api - INFO - twofa_api.py:149 - setup_verify_2fa - 用户2FA设置成功并完成登录: bearyang
2025-07-24 22:41:49,363 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-24 22:41:49,363 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-24 22:42:11,355 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-07-24 22:42:11,373 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: FJPcisVW..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-07-24 22:42:11,373 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-07-24 22:42:32,603 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=FJPcisVW...
2025-07-24 22:42:32,663 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-07-24 22:42:32,663 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: FJPcisVW...
2025-07-24 22:42:32,685 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=9Roatt98hqq4SAQCQvm9_qYmEVfaVF31XXMMpivazcY
2025-07-24 22:42:32,685 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-07-25 00:34:36,031 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 00:34:36,032 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 00:48:03,606 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 00:48:03,606 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 00:52:51,211 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 00:52:51,218 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 00:58:03,946 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 00:58:03,946 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 01:06:09,507 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 01:06:09,507 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 01:19:21,811 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 01:19:21,811 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 01:42:31,230 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 01:42:31,230 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 02:04:11,224 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 02:04:11,224 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 02:23:38,479 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 02:23:38,479 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 09:38:20,818 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 09:38:20,818 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 11:31:14,450 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 11:31:14,450 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 11:41:05,906 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 11:41:05,906 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 13:49:08,732 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 13:49:08,732 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-25 16:49:26,491 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-25 16:49:26,491 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-26 12:54:36,948 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-07-26 12:54:44,210 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-07-26 12:54:44,399 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: a0v58r3h..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-07-26 12:54:44,404 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-07-26 12:55:13,967 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=a0v58r3h...
2025-07-26 12:55:14,560 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-07-26 12:55:14,605 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: a0v58r3h...
2025-07-26 12:55:14,874 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=u3zIFiKqNheD9ycAXcA3sOecmzA333hSPUikPdXjZ6c
2025-07-26 12:55:14,881 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-07-26 13:26:53,626 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-26 13:26:53,626 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 17:05:50,902 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-07-27 17:08:37,283 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 17:08:37,283 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 17:08:56,671 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-07-27 17:08:56,820 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: 5r46yUpP..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-07-27 17:08:56,820 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-07-27 17:09:21,796 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=5r46yUpP...
2025-07-27 17:09:21,990 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=5r46yUpP...
2025-07-27 17:09:21,994 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-07-27 17:09:21,995 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: 5r46yUpP...
2025-07-27 17:09:21,998 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=_eYjQpOa2fB4juNpQwPbqQGp_QVE2desZZPUDhRkh9Q
2025-07-27 17:09:22,040 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-07-27 17:09:22,040 - modules.auth.services.totp_service - ERROR - totp_service.py:291 - verify_2fa_login - 登录2FA验证失败: TransactionContext Error: Conflict on update!
2025-07-27 17:09:22,042 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 2FA验证失败
2025-07-27 20:18:21,701 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 20:18:21,701 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 20:59:57,244 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 20:59:57,244 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 21:00:07,445 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 21:00:07,445 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 21:00:22,326 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 21:00:22,326 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 21:01:35,134 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 21:01:35,134 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 21:49:56,500 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 21:49:56,500 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 21:50:46,050 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 21:50:46,129 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 22:06:42,479 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 22:06:42,479 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-27 23:11:35,312 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-27 23:11:35,312 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 00:05:49,935 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 00:05:49,936 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 01:38:31,128 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 01:38:31,128 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 09:03:02,662 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 09:03:02,662 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 10:13:01,273 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 10:13:01,273 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 14:40:47,754 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 14:40:47,754 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 14:41:09,901 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 14:41:09,901 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 14:48:27,208 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 14:48:27,208 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 14:49:41,593 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin, IP: 127.0.0.1, 原因: 用户不存在
2025-07-28 14:56:04,565 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 14:56:04,565 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 15:04:10,564 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 15:04:10,564 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 15:04:28,570 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 15:04:28,570 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 15:14:44,260 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 15:14:44,260 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 17:16:45,501 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-07-28 17:16:47,577 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-07-28 17:16:47,760 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: WMlyJmvn..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-07-28 17:16:47,760 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-07-28 17:17:11,239 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=WMlyJmvn...
2025-07-28 17:17:11,761 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-07-28 17:17:11,761 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: WMlyJmvn...
2025-07-28 17:17:11,989 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=rSluSjVH1YL0Gbj5tQsg1sImtzwfZSkMVsM6hDufZK4
2025-07-28 17:17:11,990 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-07-28 18:53:29,607 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 18:53:29,607 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 19:01:48,487 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 19:01:48,487 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 19:06:06,252 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 19:06:06,252 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 19:08:52,686 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 19:08:52,686 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 19:09:12,750 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 19:09:12,750 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 19:18:21,048 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 19:18:21,048 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 19:20:43,140 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 19:20:43,140 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 19:22:39,074 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 19:22:39,075 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 19:34:19,692 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 19:34:19,692 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 21:41:49,022 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 21:41:49,023 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-28 21:44:11,009 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-28 21:44:11,010 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 00:24:47,953 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 00:24:47,953 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 09:47:07,871 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 09:47:07,871 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 11:24:16,110 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 11:24:16,110 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 11:24:35,770 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 11:24:35,770 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 11:52:14,163 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 11:52:14,163 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 21:22:30,308 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 21:22:30,308 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 21:22:39,500 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-07-29 21:22:41,603 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-07-29 21:22:41,779 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: QcbgXRBL..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-07-29 21:22:41,779 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-07-29 21:22:58,706 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=QcbgXRBL...
2025-07-29 21:22:59,149 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-07-29 21:22:59,149 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: QcbgXRBL...
2025-07-29 21:22:59,365 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=rRqKUuqJWuFRREQPSBqpKbJwYjAZROzF9UoERXLZYlM
2025-07-29 21:22:59,365 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-07-29 22:43:03,997 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 22:43:03,997 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 22:45:37,164 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 22:45:37,246 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-29 23:04:00,275 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-29 23:04:00,275 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-30 10:31:01,470 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-30 10:31:01,470 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-30 20:43:28,638 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-30 20:43:28,638 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-30 20:53:31,726 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-30 20:53:31,726 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
