/**
 * This file will contain the specific JavaScript logic for the contract_analysis.html page.
 * It is imported by contract-analysis-main.js.
 * 
 * Responsibilities:
 * - Initializing ECharts instances.
 * - Handling user interactions (button clicks, form submissions, input changes).
 * - Making API calls to the backend for data upload, task status, results.
 * - Dynamically updating the DOM with results and status messages.
 * - Managing pagination if applicable.
 * - Handling modal interactions (e.g., detailModal).
 */

// Ensure ECharts is available if script relies on it directly being on window scope
// const echarts = window.echarts; // Or better, pass/import echarts from contract-analysis-main.js if possible

let hasInitiatedResultDisplayAndRefresh = false; // Global flag

// 复制到剪贴板的工具函数
function copyToClipboard(text, label = '内容') {
    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代的 Clipboard API
        navigator.clipboard.writeText(text).then(() => {
            showCopySuccessMessage(label, text);
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text, label);
        });
    } else {
        // 降级方案
        fallbackCopyTextToClipboard(text, label);
    }
}

// 降级复制方案
function fallbackCopyTextToClipboard(text, label = '内容') {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 避免滚动到底部
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccessMessage(label, text);
        } else {
            showCopyErrorMessage(label);
        }
    } catch (err) {
        console.error('降级复制也失败了:', err);
        showCopyErrorMessage(label);
    }
    
    document.body.removeChild(textArea);
}

// 显示复制成功消息
function showCopySuccessMessage(label, text) {
    // 创建临时提示框
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #28a745;
        color: white;
        padding: 12px 20px;
        border-radius: 4px;
        z-index: 9999;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        max-width: 400px;
        word-break: break-all;
    `;
    toast.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 4px;">
            <i class="bi bi-check-circle"></i> ${label}已复制到剪贴板
        </div>
        <div style="font-size: 12px; opacity: 0.9;">
            ${text.length > 50 ? text.substring(0, 50) + '...' : text}
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// 显示复制失败消息
function showCopyErrorMessage(label) {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #dc3545;
        color: white;
        padding: 12px 20px;
        border-radius: 4px;
        z-index: 9999;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    `;
    toast.innerHTML = `<i class="bi bi-exclamation-triangle"></i> ${label}复制失败，请手动选择`;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

document.addEventListener('DOMContentLoaded', () => {
    // --- DOM Element References ---
    const contractFile = document.getElementById('contractFile');
    const uploadContractBtn = document.getElementById('uploadContractBtn');

    const contractAlert = document.getElementById('contractAlert');
    const taskIdSelector = document.getElementById('taskIdSelector');
    const refreshTasksBtn = document.getElementById('refreshTasksBtn');
    const loadTaskBtn = document.getElementById('loadTaskBtn');

    // Task Info Elements removed - no longer needed

    // Result Tabs & Content
    const resultTabsDiv = document.getElementById('resultTabs');
    // Chart Containers
    const contractChartDiv = document.getElementById('contractChart');
    const anomalyChartDiv = document.getElementById('anomalyChart');
    // Risk Counts
    const highRiskCountDisplay = document.getElementById('highRiskCount');
    const mediumRiskCountDisplay = document.getElementById('mediumRiskCount');
    const lowRiskCountDisplay = document.getElementById('lowRiskCount');
    // Tables
    const contractSearchInput = document.getElementById('contractSearchInput');
    const contractTableBody = document.getElementById('contractTableBody');


    // Modal
    const detailModal = document.getElementById('detailModal'); // Assuming bootstrap.Modal is used
    const detailModalBody = document.getElementById('detailModalBody');
    let detailModalInstance = null;
    if (detailModal && window.bootstrap && window.bootstrap.Modal) {
        detailModalInstance = new window.bootstrap.Modal(detailModal);
    }


    // Pagination
    const paginationControlsDiv = document.getElementById('paginationControls');
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');
    const currentPageSpan = document.getElementById('currentPage');
    const totalPagesSpan = document.getElementById('totalPages');

    // --- Global State Variables ---
    let currentContractTaskId = null;

    let pollingTimer = null;
    const POLLING_INTERVAL = 3000; // 3 seconds

    let contractChartInstance = null;
    let anomalyChartInstance = null;
    
    // 事件监听器状态管理
    let eventListenersAdded = false;
    
    // 事件处理器引用（用于正确移除和重新绑定）
    let loadTaskHandler, inputHandler;

    // --- Initialization Code ---
    initPage();

    function initPage() {
        // 强制清除localStorage缓存，避免加载旧的测试数据
        localStorage.removeItem('lastContractTaskId');
        localStorage.removeItem('taskCompleted');
        hasInitiatedResultDisplayAndRefresh = false; // Reset flag on page init
        resetUI();
        resetTaskSelector(); // 单独重置任务选择器
        
        // 确保事件监听器只设置一次
        if (!eventListenersAdded) {
            setupEventListeners();
            eventListenersAdded = true;
        }
        
        // 检查URL参数中的taskId
        const urlParams = new URLSearchParams(window.location.search);
        const taskIdFromUrl = urlParams.get('taskId');
        
        // 加载任务列表，并在加载完成后处理URL参数
        loadTaskList().then(() => {
            if (taskIdFromUrl) {
                currentContractTaskId = taskIdFromUrl;
                
                // 任务信息区域已移除，无需显示
                
                // 尝试在任务选择器中选中对应的任务
                if (taskIdSelector) {
                    const option = Array.from(taskIdSelector.options).find(opt => opt.value === taskIdFromUrl);
                    if (option) {
                        taskIdSelector.value = taskIdFromUrl;
                        
                        // 触发change事件，确保UI更新
                        taskIdSelector.dispatchEvent(new Event('change'));
                        
                        if (loadTaskBtn) {
                            loadTaskBtn.disabled = false;
                        }
                        
                        showAlert('检测到任务ID，已在选择器中选中，请点击"加载"按钮获取结果', 'info');
                        
                        // 强制DOM更新和重新渲染
                        taskIdSelector.style.display = 'none';
                        taskIdSelector.offsetHeight; // 触发重排
                        taskIdSelector.style.display = '';
                        
                        // 确保加载按钮可见且启用
                        if(loadTaskBtn) {
                            loadTaskBtn.style.display = '';
                            loadTaskBtn.disabled = false;
                        }
                    } else {
                        showAlert('检测到任务ID，但在任务列表中未找到，请刷新任务列表', 'warning');
                    }
                }
                

            } else {
                // 用户要求：在没有taskId的情况下，访问就不要自动加载taskId

                

            }
        }).catch(error => {
            showError('任务列表加载失败，无法处理URL参数');
        });
        

    }

    // --- URL状态管理函数 ---
    function updateUrlWithTaskId(taskId, forceReload = false) {
        // 检查taskId是否有效
        if (!taskId || taskId === 'undefined' || taskId === 'null') {
            console.warn('尝试使用无效的taskId更新URL:', taskId);
            return;
        }
        
        try {
            // 使用新URL构建完整地址
            const url = new URL(window.location);
            
            // 检查URL是否已经包含相同的taskId
            if (url.searchParams.get('taskId') === taskId && !forceReload) {
                return;
            }
            
            // 更新URL参数
            url.searchParams.set('taskId', taskId);
            
            // 临时禁用localStorage存储，避免缓存旧数据
            // localStorage.setItem('lastContractTaskId', taskId);
            
            // 如果需要强制刷新，则使用location.href进行页面跳转
            if (forceReload) {
                window.location.href = url.toString();
                return;
            }
            
            // 否则使用pushState更新URL（不刷新页面）
            window.history.pushState({taskId: taskId}, '', url);
            
            // 更新页面标题，便于用户在浏览器标签中识别
            document.title = `合约分析 - 任务 ${taskId}`;
        } catch (error) {
            console.error('更新URL时出错:', error);
        }
    }

    function resetUI() {
        if(contractAlert) {
            contractAlert.classList.add('d-none');
            contractAlert.textContent = '';
        }
        if(resultTabsDiv) resultTabsDiv.classList.add('d-none');
        if(paginationControlsDiv) paginationControlsDiv.classList.add('d-none');
        
        // Task info fields removed - no longer needed
        
        // Clear tables and charts
        if(contractTableBody) contractTableBody.innerHTML = '';
        if(contractChartInstance) contractChartInstance.dispose();
        if(anomalyChartInstance) anomalyChartInstance.dispose();
        contractChartInstance = null;
        anomalyChartInstance = null;

        // Reset risk counts
        if(highRiskCountDisplay) highRiskCountDisplay.textContent = '0';
        if(mediumRiskCountDisplay) mediumRiskCountDisplay.textContent = '0';
        if(lowRiskCountDisplay) lowRiskCountDisplay.textContent = '0';

        currentContractTaskId = null;
        stopTaskPolling();
    }
    
    // 新增：只重置任务选择器的函数
    function resetTaskSelector() {
        if(taskIdSelector) {
            taskIdSelector.value = '';
            if(loadTaskBtn) loadTaskBtn.disabled = true;
        }
    }

    function setupEventListeners() {
        
        // 移除可能存在的旧事件监听器，避免重复绑定
        if (uploadContractBtn) {
            // 移除旧的监听器（如果存在）
            uploadContractBtn.removeEventListener('click', handleContractUpload);
            uploadContractBtn.addEventListener('click', handleContractUpload);
        }
        
        // 任务选择器事件
        if (taskIdSelector) {
            taskIdSelector.addEventListener('change', function() {
                const selectedTaskId = this.value;
                if (loadTaskBtn) {
                    loadTaskBtn.disabled = !selectedTaskId;
                }
            });
        }
        
        // 从选择器加载任务
        if (loadTaskBtn) {
            loadTaskBtn.addEventListener('click', function() {
                const selectedTaskId = taskIdSelector.value;
                if (selectedTaskId) {
                    loadTaskStatus(selectedTaskId);
                } else {
                    showAlert('请选择一个任务', 'warning');
                }
            });
        }
        

        
        // 刷新任务列表
        if (refreshTasksBtn) {
            refreshTasksBtn.addEventListener('click', function() {
                loadTaskList();
            });
        }
        
        // Copy task ID functionality removed - no longer needed
        


        // 添加浏览器前进/后退事件监听
        window.addEventListener('popstate', (event) => {
            if (event.state && event.state.taskId) {
                // 尝试在任务选择器中选中对应的任务
                if (taskIdSelector) {
                    const option = Array.from(taskIdSelector.options).find(opt => opt.value === event.state.taskId);
                    if (option) {
                        taskIdSelector.value = event.state.taskId;
                        if (loadTaskBtn) loadTaskBtn.disabled = false;
                    }
                }
                loadTaskStatus(event.state.taskId);
            } else {
                // 如果没有状态，重置UI
                resetUI();
            }
        });

        // 事件监听器已在其他模块中实现
    }

    // --- 任务列表加载函数 ---
    async function loadTaskList() {
        try {
            
            const response = await fetch('/api/contract/tasks', {
            credentials: 'include'
        });
            const data = await response.json();
            
            if (data.status === 'success') {
                const tasks = data.tasks || [];
                updateTaskSelector(tasks);
    
                
                // 验证任务选择器是否正确更新
                if (taskIdSelector) {
                    const totalOptions = taskIdSelector.options.length;
                    const taskOptions = tasks.length;
    
                    
                    // 只有在没有正确显示任务时才报错
                    if (taskOptions > 0 && totalOptions <= 1) {
                        console.error(`❌ 任务选择器更新异常：有 ${taskOptions} 个任务但只有 ${totalOptions} 个选项`);
                    }
                } else {
                    console.error(`❌ 任务选择器元素不存在`);
                }
                
                return tasks; // 返回任务列表
            } else {
                throw new Error(data.error || '获取任务列表失败');
            }
        } catch (error) {
            console.error('加载任务列表失败:', error);
            updateTaskSelector([], '加载失败，请刷新重试');
            showAlert('加载任务列表失败: ' + error.message, 'warning');
            throw error; // 重新抛出错误，让调用者知道失败了
        }
    }
    
    // 更新任务选择器
    function updateTaskSelector(tasks, errorMessage = null) {
        if (!taskIdSelector) return;
        
        // 清空现有选项
        taskIdSelector.innerHTML = '';
        
        if (errorMessage) {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = errorMessage;
            option.disabled = true;
            taskIdSelector.appendChild(option);
            return;
        }
        
        // 添加默认选项
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = tasks.length > 0 ? '请选择要恢复的任务...' : '暂无可用任务';
        taskIdSelector.appendChild(defaultOption);
        
        // 添加任务选项
        tasks.forEach(task => {
            const option = document.createElement('option');
            option.value = task.task_id;

            // 格式化显示文本 - 支持新的任务ID格式
            const createdDate = new Date(task.created_at).toLocaleString();
            const contractCount = task.total_contracts || task.total_analyzed || 0;
            const riskCount = task.risk_contracts || task.risks_found || 0;
            const filename = task.filename || '未知文件';

            // 解析任务ID中的信息
            const taskInfo = parseTaskId(task.task_id);
            let displayText = '';

            if (taskInfo.date_range && taskInfo.data_count) {
                // 新格式：包含日期和数据量
                displayText = `${filename} [${taskInfo.date_range}日期, ${taskInfo.data_count}条数据, ${riskCount}个风险]`;
            } else {
                // 旧格式：使用原有显示方式
                displayText = `${filename} (${contractCount}个合约, ${riskCount}个风险, ${createdDate})`;
            }

            option.textContent = displayText;
            taskIdSelector.appendChild(option);
        });
        
        // 如果加载按钮存在，根据选择状态更新按钮状态
        if (loadTaskBtn) {
            loadTaskBtn.disabled = true;
        }
        
        // 强制DOM更新和重新渲染
        taskIdSelector.style.display = 'none';
        taskIdSelector.offsetHeight; // 触发重排
        taskIdSelector.style.display = '';
        

    }

    // --- 任务ID解析函数 ---
    function parseTaskId(taskId) {
        try {
            const result = {
                uuid: '',
                date_range: null,
                data_count: null,
                raw_task_id: taskId
            };

            // 新格式解析：fd465cfd5474_日期0721-0725_数据量15k
            const newFormatPattern = /^([a-f0-9]{12})(?:_日期([0-9-]+))?(?:_数据量([0-9]+k?))?$/;
            const newMatch = taskId.match(newFormatPattern);

            if (newMatch) {
                // 新格式
                result.uuid = newMatch[1];
                result.date_range = newMatch[2] || null;
                result.data_count = newMatch[3] || null;
                return result;
            }

            // 旧格式解析：按下划线分割
            const parts = taskId.split('_');
            result.uuid = parts[0] || taskId;

            if (parts.length >= 2) {
                // 判断第二部分是日期范围还是数据量
                const secondPart = parts[1];
                if (/^\d{4}(-\d{4})?$/.test(secondPart)) {
                    // 是日期范围（MMDD或MMDD-MMDD格式）
                    result.date_range = secondPart;
                } else if (/^\d+k?$/.test(secondPart)) {
                    // 是数据量
                    result.data_count = secondPart;
                }
            }

            if (parts.length >= 3) {
                // 第三部分是数据量
                result.data_count = parts[2];
            }

            return result;
        } catch (e) {
            console.warn('解析任务ID失败:', e);
            return {
                uuid: taskId,
                date_range: null,
                data_count: null,
                raw_task_id: taskId
            };
        }
    }

    // --- Alert Function ---
    function showAlert(message, type = 'info', duration = 0) {
        if (!contractAlert) return;
        contractAlert.textContent = message;
        contractAlert.className = `alert alert-${type}`;
        contractAlert.classList.remove('d-none');
        if (duration > 0) {
            setTimeout(() => contractAlert.classList.add('d-none'), duration);
        }
    }

    // --- 处理信息显示函数 ---
    function updateProcessingModeDisplay(mode) {
        const display = document.getElementById('processingModeDisplay');
        if (!display) return;

        if (mode === 'incremental') {
            display.innerHTML = '<i class="bi bi-arrow-up-circle text-success"></i> 增量处理';
        } else if (mode === 'rerun') {
            display.innerHTML = '<i class="bi bi-arrow-repeat text-warning"></i> 重跑模式';
        } else {
            // 批量模式已注销，默认显示增量处理
            display.innerHTML = '<i class="bi bi-arrow-up-circle text-success"></i> 增量处理';
        }
    }

    function updateTaskStatusDisplay(status, message = '') {
        const display = document.getElementById('taskStatusDisplay');
        if (!display) return;

        let badgeClass = 'bg-secondary';
        let icon = 'bi-clock';
        let text = message || status;

        switch (status) {
            case 'processing':
                badgeClass = 'bg-primary';
                icon = 'bi-arrow-clockwise';
                text = message || '处理中';
                break;
            case 'completed':
                badgeClass = 'bg-success';
                icon = 'bi-check-circle';
                text = message || '已完成';
                break;
            case 'failed':
                badgeClass = 'bg-danger';
                icon = 'bi-x-circle';
                text = message || '处理失败';
                break;
            case 'pending':
            default:
                badgeClass = 'bg-secondary';
                icon = 'bi-clock';
                text = message || '待处理';
                break;
        }

        display.innerHTML = `<span class="badge ${badgeClass}"><i class="${icon}"></i> ${text}</span>`;
    }

    function updateDataCompletenessDisplay(completeCount = 0, incompleteCount = 0) {
        const completeElement = document.getElementById('completePositionsCount');
        const incompleteElement = document.getElementById('incompletePositionsCount');

        if (completeElement) completeElement.textContent = completeCount;
        if (incompleteElement) incompleteElement.textContent = incompleteCount;
    }

    function updateProcessingTimeDisplay(timeText) {
        const display = document.getElementById('processingTimeDisplay');
        if (display) {
            display.textContent = timeText || '--';
        }
    }

    // --- API Call Functions (examples, implement fully based on app.py) ---
    function handleContractUpload() {
        if (!contractFile || !contractFile.files || contractFile.files.length === 0) {
            showAlert('请选择一个文件进行上传', 'warning');
            return;
        }

        // 获取选择的处理模式
        const processingModeRadios = document.getElementsByName('processingMode');
        let processingMode = 'incremental'; // 默认值
        for (const radio of processingModeRadios) {
            if (radio.checked) {
                processingMode = radio.value;
                break;
            }
        }

        hasInitiatedResultDisplayAndRefresh = false; // 在新上传时重置标志

        const file = contractFile.files[0];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('processing_mode', processingMode); // 添加处理模式参数

        resetUI(); // 在新上传前重置UI

        // 更新处理信息显示
        updateProcessingModeDisplay(processingMode);
        updateTaskStatusDisplay('processing', '正在处理...');

        const modeText = processingMode === 'incremental' ? '增量处理' : '重跑模式';
        showAlert(`正在上传和处理文件... (${modeText})`, 'info');
        // 任务状态显示已移除
        
                    fetch('/api/contract/upload', { 
                        method: 'POST', 
                        body: formData,
                        credentials: 'include'
                    })
            .then(response => {
                // 首先检查HTTP状态码
                if (!response.ok) {
                    if (response.status === 413) {
                        throw new Error('文件过大，请确保文件大小不超过2GB');
                    } else if (response.status === 400) {
                        return response.json().then(data => {
                            throw new Error(data.error || '请求参数错误');
                        });
                    } else if (response.status === 500) {
                        throw new Error('服务器内部错误，请稍后重试');
                    } else {
                        throw new Error(`上传失败，状态码: ${response.status}`);
                    }
                }
                
                // 检查响应内容类型
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('服务器返回了无效的响应格式');
                }
                
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }

                showAlert(`文件上传成功，任务ID: ${data.task_id}，正在处理分析...`, 'success');
                
                // 更新任务ID
                currentContractTaskId = data.task_id;
                // 任务信息显示已移除
                

                
                // 更新URL而不刷新页面，避免事件监听器丢失
                updateUrlWithTaskId(data.task_id);
                
                // 重新加载任务列表，这样新任务会出现在选择器中
                loadTaskList();
                
                // 首次上传时开始轮询任务状态
                startTaskPolling(data.task_id, 'contract');
            })
            .catch(error => {
                console.error('[UPLOAD_DEBUG] 上传错误:', error);
                showAlert(`上传失败: ${error.message}`, 'danger');
                // 任务状态显示已移除
                hasInitiatedResultDisplayAndRefresh = false; // 确保错误处理时重置标志
            });
    }



    function loadTaskStatus(taskId) {
        // 只重置结果显示区域，不重置任务选择器
        resetUI();
        showAlert(`正在加载任务 ${taskId} 的状态...`, 'info');
        currentContractTaskId = taskId;
        // 任务信息显示已移除
        
        // 临时禁用localStorage存储
        // localStorage.setItem('lastContractTaskId', taskId);
        
        // 更新URL而不刷新页面
        updateUrlWithTaskId(taskId);
        
        // 手动选择任务时，直接获取任务状态，不自动轮询
        fetch(`/api/task/${taskId}`, {
            credentials: 'include'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`服务器返回状态码 ${response.status}`);
                }
                return response.json();
            })
            .then(data => {

                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                updateTaskProgress(data, 'contract');
                
                // 如果任务已完成，直接获取结果
                if (data.status === 'completed') {
                    fetchTaskResult(taskId, 'contract');
                } else if (data.status === 'failed') {
                    showAlert(`任务执行失败: ${data.message || '未知错误'}`, 'danger');
                } else {
                    // 任务未完成，显示状态但不自动轮询
                    showAlert(`任务状态: ${data.status}，如需实时更新请重新点击加载`, 'info');
                }
            })
            .catch(error => {
                console.error('加载任务状态失败:', error);
                showAlert(`加载任务状态失败: ${error.message}`, 'danger');
                // 任务状态显示已移除
            });
    }

    // 首次上传后的智能轮询机制
    function startTaskPolling(taskId, taskType) {
        stopTaskPolling(); // 清除现有轮询
        
        // 如果是合约任务，确保URL已更新
        if (taskType === 'contract' && taskId) {
            currentContractTaskId = taskId;
            updateUrlWithTaskId(taskId);
        }
        
        function checkTaskStatus() {
            
            fetch(`/api/contract/status/${taskId}`, {
                credentials: 'include'
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`服务器返回状态码 ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    
                    updateTaskProgress(data, taskType);
                    
                    // 检查任务状态
                    if (data.status === 'completed') {
                        stopTaskPolling();
                        fetchTaskResult(taskId, taskType);
                    } else if (data.status === 'failed') {
                        stopTaskPolling();
                        showAlert(`任务执行失败: ${data.message || '未知错误'}`, 'danger');
                    } else {
                        pollingTimer = setTimeout(checkTaskStatus, POLLING_INTERVAL);
                    }
                })
                .catch(error => {
                    console.error('❌ 轮询任务状态失败:', error);
                    showAlert(`轮询任务状态失败: ${error.message}`, 'danger');
                    // 任务状态显示已移除
                    stopTaskPolling();
                });
        }
        
        // 立即执行第一次检查
        checkTaskStatus();
    }

    // 保留原有的轮询函数用于关联任务
    function startPolling(taskId, taskType) { // taskType can be 'contract' or 'association'
        stopPolling(); // Clear any existing timer
        
        // 如果是合约任务，确保URL已更新
        if (taskType === 'contract' && taskId) {
            currentContractTaskId = taskId;
            updateUrlWithTaskId(taskId);
        }
        
        function poll() {
            
            fetch(`/api/contract/status/${taskId}`, {
                credentials: 'include'
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`服务器返回状态码 ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    
                    // 检查数据有效性
                    if (!data || typeof data !== 'object') {
                        console.warn('收到无效任务数据格式');
                        throw new Error('无效任务数据格式');
                    }
                    
                    updateTaskProgress(data, taskType);
                    
                    // 检查任务状态
                    if (data.status === 'completed' || data.status === 'failed') {
                        stopPolling();
                        
                        if (data.status === 'completed') {
                            fetchTaskResult(taskId, taskType);                            
                        } else {
                            showAlert(`任务执行失败: ${data.message || '未知错误'}`, 'danger');
                        }
                    } else {
                        pollingTimer = setTimeout(poll, POLLING_INTERVAL);
                    }
                })
                .catch(error => {
                    console.error('轮询任务状态失败:', error);
                    showAlert(`轮询任务状态失败: ${error.message}`, 'danger');
                    // 任务状态显示已移除
                    stopPolling();
                });
        }
        
        // 立即执行第一次轮询
        poll();
    }

    function stopTaskPolling() {
        if (pollingTimer) {
            clearTimeout(pollingTimer);
            pollingTimer = null;
        }
    }

    function stopPolling() {
        if (pollingTimer) {
            clearTimeout(pollingTimer);
            pollingTimer = null;
        }
    }

    // updateTaskStatusDisplay 函数已移除 - 任务信息显示已不再需要

    function updateTaskProgress(data, taskType) {
        
        // 状态文本本地化
        let statusText = data.status || '未知';
        if (statusText === 'processing') statusText = '处理中';
        else if (statusText === 'completed') statusText = '完成';
        else if (statusText === 'pending') statusText = '等待中';
        else if (statusText === 'failed') statusText = '失败';
        
        // 任务状态显示已移除

        // 如果是合约任务且有任务ID，确保URL已更新
        if (taskType === 'contract' && data.task_id) {
            currentContractTaskId = data.task_id;
            updateUrlWithTaskId(data.task_id);
        }

        // 任务信息显示已移除
        
        // 如果任务完成且有结果，自动获取结果
        if (statusText === '完成' && data.has_result === true) {
            fetchTaskResult(currentContractTaskId, taskType);
        }
    }

    // 测试数据生成函数已删除 - 仅使用后端真实数据

    function fetchTaskResult(taskId, taskType) {
        // 1. 在函数最开始检查全局标志
        if (hasInitiatedResultDisplayAndRefresh) {
            return;
        }
        
        showAlert(`任务 ${taskId} 完成，正在获取结果...`, 'info');
        
        // 已删除测试数据逻辑 - 仅使用后端真实数据
        
        // 使用分页加载结果
        loadTaskResultWithPagination(taskId);
    }

    // 加载任务结果 - 支持真正的分页模式
    async function loadTaskResultWithPagination(taskId, page = 1) {
        try {
            // 设置全局标志防止重复执行
            hasInitiatedResultDisplayAndRefresh = true;
            
            const response = await fetch(`/api/contract/result/${taskId}?page=${page}&page_size=50`, {
                credentials: 'include'
            });
            
            if (!response.ok) {
                throw new Error(`服务器返回状态码: ${response.status}`);
            }
            
            const data = await response.json();
            if (data.error) throw new Error(data.error);
            
            // 临时禁用localStorage存储
            // if (page === 1 && data.task_id) {
            //     localStorage.setItem('lastContractTaskId', data.task_id);
            //     localStorage.setItem('taskCompleted', 'true');
            // }
            
            // 显示结果
            if(resultTabsDiv) resultTabsDiv.classList.remove('d-none');
            
            // 保存分页信息到全局变量，供分页控件使用
            window.currentContractPagination = data.pagination;
            window.currentContractTaskId = taskId;
            
            // 显示当前页数据
            displayResults(data, true); // 第二个参数表示这是分页数据
            
            if (page === 1) {
                showAlert(`任务结果获取成功！共 ${data.pagination.total_records || 0} 条记录`, 'success', 2000);
            }
            
            // 重置标志
            hasInitiatedResultDisplayAndRefresh = false;
            
        } catch (error) {
            console.error('分页加载失败:', error);
            showAlert(`获取任务结果失败: ${error.message}`, 'danger');
            hasInitiatedResultDisplayAndRefresh = false;
        }
    }

    function displayResults(results, isPaginatedData = false) {
        try {
            // 参数验证
            if (!results) {
                console.error('没有结果数据可显示');
                return;
            }
            
            // 确保结果标签可见
            try {
                if (resultTabsDiv) resultTabsDiv.classList.remove('d-none');
            } catch (e) {
                console.error('更新UI元素时出错:', e);
            }
            
            // 确保URL已更新
            try {
                if (currentContractTaskId && currentContractTaskId !== 'undefined') {
                    updateUrlWithTaskId(currentContractTaskId);
                } else if (results.task_id) {
                    currentContractTaskId = results.task_id;
                    updateUrlWithTaskId(results.task_id);
                }
            } catch (e) {
                console.error('更新URL时出错:', e);
            }

            // 提取实际结果数据，兼容多种返回格式
            let resultData = null;
            let suspiciousData = [];
            

            
            // 这里之后的代码保持原样，只增加一个try-catch包裹整体逻辑
            try {
                // 检查API返回的数据结构，从多种可能的位置提取可疑交易数据
                if (results.result && results.result.suspicious && Array.isArray(results.result.suspicious)) {
                    // API直接返回的格式 - 最常见的情况
                    suspiciousData = results.result.suspicious;
                    resultData = results.result;

                } else if (results.suspicious && Array.isArray(results.suspicious)) {
                    // 如果suspicious直接在results中
                    suspiciousData = results.suspicious;
                    resultData = results;
                } else if (results.completed_at && results.result) {
                    // 直接检查顶层result字段
                    if (results.result.suspicious && Array.isArray(results.result.suspicious)) {
                        suspiciousData = results.result.suspicious;
                        resultData = results.result;
                    }
                } else {
                    console.error('未识别的数据格式，无法显示结果');
                    return;
                }
                
                // 验证数据获取结果
                if (suspiciousData.length === 0) {
                    console.warn('没有找到任何可疑交易数据');
                }
                
                // 构建摘要数据
                const summaryData = {
                    risk_distribution: { high: 0, medium: 0, low: 0 },
                    contract_distribution: {},
                    anomaly_distribution: {}
                };
                
                // 如果已有summary数据，优先使用
                // 先检查results.result.summary(后端标准格式)
                if (results.result && results.result.summary) {
        
                    
                    // 处理风险分布数据
                    if (results.result.summary.risk_distribution) {
                        summaryData.risk_distribution = results.result.summary.risk_distribution;
    
                    } 
                    // 检查summary是否直接就是风险分布数据(兼容旧格式)
                    else if (typeof results.result.summary === 'object' && 
                             (results.result.summary.high !== undefined || 
                              results.result.summary.medium !== undefined || 
                              results.result.summary.low !== undefined)) {
                        summaryData.risk_distribution = {
                            high: results.result.summary.high || 0,
                            medium: results.result.summary.medium || 0,
                            low: results.result.summary.low || 0
                        };
                    }
                    
                    // 处理异常类型分布数据（关键修复：确保总是检查anomaly_distribution）
                    if (results.result.summary.anomaly_distribution) {
                        summaryData.anomaly_distribution = results.result.summary.anomaly_distribution;
                    }
                    
                    // 合约分布数据在contract_stats字段中
                    if (results.result.contract_stats) {
                        summaryData.contract_distribution = results.result.contract_stats;
                    }
                }
                

                
                // 屏蔽从分页数据生成异常类型分布（防止显示错误的50个高频交易）
                if (suspiciousData && suspiciousData.length > 0 && 
                    Object.keys(summaryData.anomaly_distribution).length === 0) {
                    // 不从分页数据生成异常类型分布，避免误导用户
                }
                // 再检查results.summary(兼容旧格式)
                else if (results.summary) {
                    if (results.summary.risk_distribution) {
                        summaryData.risk_distribution = results.summary.risk_distribution;
                    }
                    if (results.summary.contract_distribution) {
                        summaryData.contract_distribution = results.summary.contract_distribution;
                    }
                    if (results.summary.anomaly_distribution) {
                        summaryData.anomaly_distribution = results.summary.anomaly_distribution;
                    }
                }
                // 如果后端没有提供summary，但有可疑交易数据，则在前端生成摘要
                else if (suspiciousData && suspiciousData.length > 0) {
                    
                    suspiciousData.forEach(item => {
                        // 1. Risk Distribution
                        // Ensure risk_distribution is initialized for all expected levels
                        summaryData.risk_distribution.high = summaryData.risk_distribution.high || 0;
                        summaryData.risk_distribution.medium = summaryData.risk_distribution.medium || 0;
                        summaryData.risk_distribution.low = summaryData.risk_distribution.low || 0;

                        const riskLevel = item.severity || item.risk_level || 'low'; 
                        if (summaryData.risk_distribution.hasOwnProperty(riskLevel)) {
                            summaryData.risk_distribution[riskLevel]++;
                        } else {
                            summaryData.risk_distribution['low']++; // Default for unknown levels
                        }

                        // 2. Contract Distribution (by count of occurrences)
                        const contractName = item.contractName || item.contract_name || '未知合约';
                        summaryData.contract_distribution[contractName] = (summaryData.contract_distribution[contractName] || 0) + 1;

                        // 3. Anomaly Distribution (屏蔽：避免从分页数据生成误导性统计)
                        // const anomalyType = item.detection_type || item.anomaly_type || '未知类型';
                        // summaryData.anomaly_distribution[anomalyType] = (summaryData.anomaly_distribution[anomalyType] || 0) + 1;
                    });
                }
                
                // 1. 更新摘要标签（计数和图表）
                if (highRiskCountDisplay) highRiskCountDisplay.textContent = summaryData.risk_distribution.high || 0;
                if (mediumRiskCountDisplay) mediumRiskCountDisplay.textContent = summaryData.risk_distribution.medium || 0;
                if (lowRiskCountDisplay) lowRiskCountDisplay.textContent = summaryData.risk_distribution.low || 0;

                // 更新增量处理相关信息
                if (resultData) {
                    // 更新处理模式显示
                    const processingMode = resultData.processing_mode || 'incremental';  // 默认改为增量模式
                    updateProcessingModeDisplay(processingMode);

                    // 更新数据完整性信息
                    const completeCount = resultData.complete_positions_count || 0;
                    const incompleteCount = resultData.incomplete_positions_count || 0;
                    updateDataCompletenessDisplay(completeCount, incompleteCount);

                    // 更新处理时间
                    if (resultData.processing_time) {
                        updateProcessingTimeDisplay(`${resultData.processing_time.toFixed(2)}秒`);
                    } else if (results.completed_at && results.created_at) {
                        const startTime = new Date(results.created_at);
                        const endTime = new Date(results.completed_at);
                        const duration = (endTime - startTime) / 1000;
                        updateProcessingTimeDisplay(`${duration.toFixed(2)}秒`);
                    }

                    // 更新任务状态
                    updateTaskStatusDisplay('completed', '分析完成');
                }
                
                // 处理图表数据
                if (contractChartDiv) {
                    renderContractBarChart(contractChartDiv, '合约交易分布', summaryData.contract_distribution, 'contractChartInstance');
                }
                
                if (anomalyChartDiv) {
                    
                    const anomalyData = summaryData.anomaly_distribution;
                    
                    // 简化逻辑：只显示后端提供的数据，如果没有数据就显示提示
                    if (Object.keys(anomalyData).length === 0) {
                        anomalyChartDiv.innerHTML = `
                            <div class="alert alert-info d-flex align-items-center">
                                <i class="bi bi-info-circle me-2"></i>
                                <div>
                                    <strong>暂无异常类型分布数据</strong><br>
                                    异常类型分布数据正在统计中，请稍后刷新页面查看。
                                </div>
                            </div>
                        `;
                    } else {
                        renderPieChart(anomalyChartDiv, '异常类型分布', summaryData.anomaly_distribution, 'anomalyChartInstance');
                    }
                }

                // 2. 填充合约异常表格
                if (contractTableBody) {
                    
                    // 异常类型英文到中文的映射表
                    const anomalyTypeMapping = {
                        'high_frequency_trading': '高频交易',
                        'regular_brush_trading': '规律性刷量',
                        'wash_trading': '对敲交易',
                        'funding_rate_arbitrage': '资金费率交易',
                        'suspected_wash_trading': '对敲交易',
                        // 新增区分后的对敲类型
                        '同账户对敲': '同账户对敲',
                        '跨账户对敲': '跨账户对敲'
                    };
                    
                    // 确定要展示的列名 - 兼容不同的字段命名
                    const columnMapping = {
                        'memberId': ['memberId', 'member_id', 'user_id'],
                        'contractName': ['contractName', 'contract_name'],
                        'detection_type': ['detection_type', 'anomaly_type'],
                        'timeRange': ['timeRange', 'time_range'],
                        'risk_level': ['severity', 'risk_level'],
                        'abnormal_volume': ['abnormal_volume', 'abnormalVolume', 'abnormal_volume_usdt'], // 异常交易量字段
                        'abnormal_cause': ['reason'] // 异常原因字段
                    };
                    
                    // 根据截图确定表格显示顺序（添加增量处理相关列）
                    const displayOrder = ['memberId', 'contractName', 'detection_type', 'timeRange', 'abnormal_volume', 'risk_level', 'data_completeness', 'transaction_frequency', 'abnormal_cause'];
                    
                    // 过滤掉无效的总结行数据
                    const filteredData = suspiciousData.filter(item => {
                        const userId = getMappedValue(item, columnMapping['memberId']);
                        // 过滤掉memberId为nan、null、undefined或包含总结信息的行
                        if (!userId || userId === 'nan' || userId === 'null' || userId.toString().toLowerCase() === 'nan') {
                            return false;
                        }
                        
                        const reason = item.reason || '';
                        if (reason.includes('检测到') && reason.includes('个高风险算法触发')) {
                            return false;
                        }
                        
                        return true;
                    });
                    

                    
                    // 格式化用户ID和异常类型前的预处理
                    const processedData = filteredData.map(item => {
                        const newItem = {...item}; // 复制原始数据

                        // 🔧 对敲交易数据增强处理
                        if (item.detection_method && item.detection_method.includes('wash_trading')) {
                            // 处理详细的交易对信息
                            if (item.trade_pair_detail) {
                                const detail = item.trade_pair_detail;

                                // 显示详细的盈亏信息
                                if (detail.user_a && detail.user_b) {
                                    newItem.profit_info = `A: ${detail.user_a.profit || 0} | B: ${detail.user_b.profit || 0}`;
                                }

                                // 显示时间差信息
                                if (detail.time_gaps) {
                                    newItem.time_gap_info = `开仓: ${detail.time_gaps.open_gap_seconds}s | 平仓: ${detail.time_gaps.close_gap_seconds}s`;
                                }

                                // 显示风险评分
                                if (item.profit_hedge_score !== undefined && item.wash_score !== undefined) {
                                    newItem.risk_scores = `对冲: ${(item.profit_hedge_score * 100).toFixed(1)}% | 综合: ${(item.wash_score * 100).toFixed(1)}%`;
                                }
                            }

                            // 🔧 修复异常交易量显示
                            if (item.abnormal_volume && typeof item.abnormal_volume === 'number') {
                                newItem.abnormal_volume = `${item.abnormal_volume.toFixed(2)} USDT`;
                            } else if (item.abnormal_volume && !item.abnormal_volume.includes('USDT')) {
                                // 如果是纯数字字符串，添加USDT后缀
                                const numValue = parseFloat(item.abnormal_volume);
                                if (!isNaN(numValue) && numValue > 0) {
                                    newItem.abnormal_volume = `${numValue.toFixed(2)} USDT`;
                                }
                            }

                            // 🔧 修复时间范围显示
                            if (item.trade_pair_detail && item.trade_pair_detail.user_a && item.trade_pair_detail.user_a.open_time) {
                                try {
                                    const openTime = new Date(item.trade_pair_detail.user_a.open_time);
                                    newItem.time_range = openTime.toLocaleString('zh-CN', {
                                        month: '2-digit', day: '2-digit',
                                        hour: '2-digit', minute: '2-digit'
                                    });
                                } catch (e) {
                                    // 时间解析失败，保持原值
                                }
                            }

                            // 🔧 修复交易数量显示
                            if (item.trade_pair_detail && !item.trade_count) {
                                newItem.trade_count = 2; // 基础交易对数量
                            }
                        }
                        
                        // 保留原始用户ID，不在数据处理阶段格式化
                        // 格式化将在显示阶段进行，以保证复制功能能获取到完整ID
                        const userId = getMappedValue(item, columnMapping['memberId']);
                        if (userId) {
                            // 保留完整的用户ID到数据中
                            columnMapping['memberId'].forEach(field => {
                                if (item[field]) {
                                    newItem[field] = userId; // 保存完整的用户ID
                                }
                            });
                            // 将完整用户ID存储到专门的字段中，供复制使用
                            newItem._originalMemberId = userId;
                        }
                        
                        // 异常类型转换为中文 - 区分同账户和跨账户对敲
                        const detectionType = getMappedValue(item, columnMapping['detection_type']);
                        const detectionMethod = item.detection_method || '';
                        
                        if (detectionType) {
                            let chineseType;
                            
                            // 对于对敲类型，根据检测方法进一步区分
                            if (detectionType === 'wash_trading' || detectionType === 'suspected_wash_trading') {
                                if (detectionMethod.includes('same_account')) {
                                    chineseType = '同账户对敲';
                                } else if (detectionMethod.includes('cross_account') || detectionMethod.includes('cross_bd')) {
                                    chineseType = '跨账户对敲';
                                } else {
                                    // 如果没有明确的检测方法，尝试从对手方ID判断
                                    const counterpartyIds = item.counterparty_ids || [];
                                    const memberId = getMappedValue(item, columnMapping['memberId']);
                                    
                                    if (!counterpartyIds.length || (counterpartyIds.length === 1 && counterpartyIds[0] === memberId)) {
                                        chineseType = '同账户对敲';
                                    } else {
                                        chineseType = '跨账户对敲';
                                    }
                                }
                            } else {
                                chineseType = anomalyTypeMapping[detectionType] || detectionType;
                            }
                            
            
                            
                            // 设置所有可能的字段名
                            columnMapping['detection_type'].forEach(field => {
                                if (item[field] !== undefined) {
                                    newItem[field] = chineseType;
                                }
                            });
                        }
                        
                        // 格式化时间范围：去除年份，分3行显示格式 (统一支持多种格式)
                        const timeRange = getMappedValue(item, columnMapping['timeRange']);
                        if (timeRange) {
                            let formattedTimeRange = null;
                            
                            // 1. ISO格式匹配：YYYY-MM-DDTHH:MM:SS - YYYY-MM-DDTHH:MM:SS (对敲交易格式)
                            let timeRangeMatch = timeRange.match(/(\d{4})-(\d{2})-(\d{2})T(\d{2}:\d{2}:\d{2}) - (\d{4})-(\d{2})-(\d{2})T(\d{2}:\d{2}:\d{2})/);
                            if (timeRangeMatch) {
                                // 提取月日和时间，去掉秒数：MM-DD HH:MM
                                const startTime = timeRangeMatch[4].substring(0, 5); // HH:MM:SS -> HH:MM
                                const endTime = timeRangeMatch[8].substring(0, 5);   // HH:MM:SS -> HH:MM
                                const startDateTime = `${timeRangeMatch[2]}-${timeRangeMatch[3]} ${startTime}`;  // MM-DD HH:MM
                                const endDateTime = `${timeRangeMatch[6]}-${timeRangeMatch[7]} ${endTime}`;      // MM-DD HH:MM
                                formattedTimeRange = `${startDateTime}|-|${endDateTime}`;
                            } else {
                                // 2. 标准格式匹配：YYYY-MM-DD HH:MM:SS - YYYY-MM-DD HH:MM:SS
                                timeRangeMatch = timeRange.match(/(\d{4})-(\d{2})-(\d{2}) (\d{2}:\d{2}:\d{2}) - (\d{4})-(\d{2})-(\d{2}) (\d{2}:\d{2}:\d{2})/);
                                if (timeRangeMatch) {
                                    // 提取月日和时间，去掉秒数：MM-DD HH:MM
                                    const startTime = timeRangeMatch[4].substring(0, 5); // HH:MM:SS -> HH:MM
                                    const endTime = timeRangeMatch[8].substring(0, 5);   // HH:MM:SS -> HH:MM
                                    const startDateTime = `${timeRangeMatch[2]}-${timeRangeMatch[3]} ${startTime}`;  // MM-DD HH:MM
                                    const endDateTime = `${timeRangeMatch[6]}-${timeRangeMatch[7]} ${endTime}`;      // MM-DD HH:MM
                                    formattedTimeRange = `${startDateTime}|-|${endDateTime}`;
                                } else {
                                    // 3. 简化格式匹配：YYYY-MM-DD HH:MM - YYYY-MM-DD HH:MM (循环交易等格式)
                                    timeRangeMatch = timeRange.match(/(\d{4})-(\d{2})-(\d{2}) (\d{2}:\d{2}) - (\d{4})-(\d{2})-(\d{2}) (\d{2}:\d{2})/);
                                    if (timeRangeMatch) {
                                        // 提取月日和时间：MM-DD HH:MM
                                        const startDateTime = `${timeRangeMatch[2]}-${timeRangeMatch[3]} ${timeRangeMatch[4]}`;  // MM-DD HH:MM
                                        const endDateTime = `${timeRangeMatch[6]}-${timeRangeMatch[7]} ${timeRangeMatch[8]}`;    // MM-DD HH:MM
                                        formattedTimeRange = `${startDateTime}|-|${endDateTime}`;
                                    }
                                }
                            }
                            
                            // 如果成功格式化，设置结果
                            if (formattedTimeRange) {
                                newItem.timeRange = formattedTimeRange;
                            }
                        }
                        
                        // 将风险等级也标准化
                        const riskLevel = getMappedValue(item, columnMapping['risk_level']);
                        if (riskLevel) {
                            let standardRiskLevel = riskLevel.toLowerCase();
                            if (standardRiskLevel === 'high') {
                                standardRiskLevel = '高';
                            } else if (standardRiskLevel === 'medium') {
                                standardRiskLevel = '中';
                            } else if (standardRiskLevel === 'low') {
                                standardRiskLevel = '低';
                            }
                            
                            columnMapping['risk_level'].forEach(field => {
                                if (item[field] !== undefined) {
                                    newItem[field] = standardRiskLevel;
                                }
                            });
                        }
                        
                        // 提取异常交易量和异常原因
                        const { volume, reason } = extractVolumeAndReason(item);
                        
                        // 设置异常交易量
                        if (volume) {
                            newItem.abnormal_volume = volume;
                        }
                        
                        // 设置异常原因（不包含交易量）
                        if (reason) {
                            newItem.abnormal_cause = reason;
                        }

                        // 添加增量处理相关字段
                        // 数据完整性
                        if (item.is_position_complete !== undefined) {
                            newItem.data_completeness = item.is_position_complete ?
                                '<span class="badge bg-success">完整</span>' :
                                '<span class="badge bg-warning">不完整</span>';
                        } else {
                            newItem.data_completeness = '<span class="badge bg-secondary">未知</span>';
                        }

                        // 交易频率
                        if (item.total_transactions && item.duration_minutes) {
                            const frequency = (item.total_transactions / (item.duration_minutes / 60)).toFixed(1);
                            newItem.transaction_frequency = `${frequency}次/小时`;
                        } else if (item.same_minute_transactions) {
                            newItem.transaction_frequency = `${item.same_minute_transactions}次/分钟`;
                        } else {
                            newItem.transaction_frequency = '数据缺失';
                        }

                        return newItem;
                    });
                    
                    // 辅助函数：提取异常交易量和异常原因
                    function extractVolumeAndReason(item) {
                        // 准备返回的结果
                        let volume = '';
                        let reason = '';
                        
                        // 获取原始原因字段
                        if (item.reason) {
                            reason = item.reason;
                        }
                        
                        // 查找可能包含异常交易量的所有字段
                        for (const field of ['abnormal_volume', 'abnormalVolume', 'abnormal_volume_usdt']) {
                            if (item[field] !== undefined && item[field] !== null) {
                                const numValue = parseFloat(item[field]);
                                if (!isNaN(numValue)) {
                                    volume = new Intl.NumberFormat('zh-CN').format(Math.round(numValue)) + ' USDT';
                                    break;
                                }
                            }
                        }
                        
                        // 如果没有从字段中找到交易量，尝试从原因文本中提取
                        if (!volume && reason) {
                            // 匹配"总量XXX USDT"或"交易量XXX USDT"格式
                            const volumeMatch = reason.match(/(总量|交易量)\s*([\d,\.]+)\s*USDT/);
                            if (volumeMatch && volumeMatch[2]) {
                                volume = volumeMatch[2] + ' USDT';
                                
                                // 从原因中移除交易量部分，保留前面的描述
                                const beforeVolumeMatch = reason.match(/(.*?)，\s*(总量|交易量)/);
                                if (beforeVolumeMatch && beforeVolumeMatch[1]) {
                                    reason = beforeVolumeMatch[1];
                                } else {
                                    // 如果无法匹配前面部分，移除整个交易量部分
                                    reason = reason.replace(/，\s*(总量|交易量)\s*[\d,\.]+\s*USDT/, '');
                                }
                            }
                        }
                        
                        // 如果原因中包含"在X分钟内频繁交易，平均每分钟Y次"，保留这部分
                        const freqMatch = reason.match(/(在\d+分钟内(?:频繁)?交易，平均每分钟[\d\.]+次).*((?:总量|交易量)[\d,\.]+\s*USDT)/);
                        if (freqMatch) {
                            // 只保留频率部分
                            reason = freqMatch[1];
                            
                            // 如果还没有提取到交易量，从匹配中提取
                            if (!volume) {
                                const volumeValue = freqMatch[2].replace(/(总量|交易量)/, '').trim();
                                volume = volumeValue;
                            }
                        }
                        
                        // 新格式：在X小时X分钟时间范围内，检测到Y个高频交易分钟
                        const newFreqMatch = reason.match(/(在[\d小时]*\d+分钟时间范围内，检测到\d+个高频交易分钟，平均每分钟[\d\.]+次).*((?:总量|交易量)[\d,\.]+\s*USDT)/);
                        if (newFreqMatch) {
                            // 只保留频率部分
                            reason = newFreqMatch[1];
                            
                            // 如果还没有提取到交易量，从匹配中提取
                            if (!volume) {
                                const volumeValue = newFreqMatch[2].replace(/(总量|交易量)/, '').trim();
                                volume = volumeValue;
                            }
                        }
                        
                        // 循环交易格式: 疑似循环交易，总利润X USDT，时间跨度Y分钟
                        const loopMatch = reason.match(/(疑似循环交易.*?时间跨度[\d\.]+分钟).*(总利润[\d,\.]+\s*USDT)/);
                        if (loopMatch) {
                            // 只保留描述部分
                            reason = loopMatch[1];
                            
                            // 如果还没有提取到交易量，从匹配中提取
                            if (!volume) {
                                const volumeValue = loopMatch[2].replace('总利润', '').trim();
                                volume = volumeValue;
                            }
                        }
                        
                        // 资金费套利格式: 疑似资金费套利，交易量X USDT
                        const fundingMatch = reason.match(/(疑似资金费套利.*?)，((?:总量|交易量)[\d,\.]+\s*USDT)/);
                        if (fundingMatch) {
                            // 只保留描述部分
                            reason = fundingMatch[1];
                            
                            // 如果还没有提取到交易量，从匹配中提取
                            if (!volume) {
                                const volumeValue = fundingMatch[2].replace(/(总量|交易量)/, '').trim();
                                volume = volumeValue;
                            }
                        }
                        
                        // 刷量交易格式: 疑似刷量交易，短持仓比例X%，平均持仓时间Y分钟，交易量Z USDT
                        const brushMatch = reason.match(/(疑似刷量交易，短持仓比例.*?平均持仓时间[\d\.]+分钟).*((?:总量|交易量)[\d,\.]+\s*USDT)/);
                        if (brushMatch) {
                            // 只保留描述部分
                            reason = brushMatch[1];
                            
                            // 如果还没有提取到交易量，从匹配中提取
                            if (!volume) {
                                const volumeValue = brushMatch[2].replace(/(总量|交易量)/, '').trim();
                                volume = volumeValue;
                            }
                        }
                        
                        // 确保原因不为空
                        if (!reason) {
                            const detectionType = getMappedValue(item, columnMapping['detection_type']);
                            reason = detectionType ? `检测到${detectionType}行为` : '检测到异常交易行为';
                        }
                        
                        // 如果没有提取到交易量，提供一个默认值
                        if (!volume) {
                            volume = '数据缺失';
                        }
                        
                        return { volume, reason };
                    }
                    
                    // 辅助函数：从映射中获取值
                    function getMappedValue(item, mappings) {
                        if (!mappings) return null;
                        for (const field of mappings) {
                            if (item[field] !== undefined) {
                                return item[field];
                            }
                        }
                        return null;
                    }
                    
                    populateTable(contractTableBody, processedData, displayOrder, true, columnMapping, isPaginatedData);
                }
                
                // 显示结果标签和分页（如果有数据）
                if(resultTabsDiv) {
                    resultTabsDiv.classList.remove('d-none');
    
                    
                    // 检查是否有可疑交易数据可显示
                    const hasData = suspiciousData && suspiciousData.length > 0;
                          
                    if (!hasData) {
        
                        showAlert('分析完成，但没有发现异常数据', 'info');
                    } else {
                        // 任务信息显示已移除，只在控制台输出
                        if (resultData && resultData.processed_rows) {
                        }
                    }
                }
            } catch (e) {
                console.error('[DISPLAY_RESULTS_DEBUG] 处理数据结构时出错:', e);
            }
        } catch (e) {
            console.error('[DISPLAY_RESULTS_DEBUG] 显示结果时发生异常:', e);
        } finally {

        }
    }
    
    function renderPieChart(element, title, data, chartInstanceKey) {
        
        
        // 检查element是否存在
        if (!element) {
            console.error(`图表容器元素不存在: ${title}`);
            return;
        }
        
        // 检查数据是否有效并且不为空
        if (!data || Object.keys(data).length === 0) {
            console.warn(`图表数据为空: ${title}`);
            element.innerHTML = `<div class="alert alert-warning">没有${title}数据可显示</div>`;
            return;
        }
        
        try {
            // 如果存在旧图表实例，销毁它
            if (window[chartInstanceKey]) {
                window[chartInstanceKey].dispose();
            }
            
            // 检查echarts是否存在
            if (typeof echarts === 'undefined') {
                console.error('ECharts库未加载');
                element.innerHTML = '<div class="alert alert-danger">图表库未加载</div>';
                return;
            }
            
            // 异常类型英文到中文的映射表
            const anomalyTypeMapping = {
                'high_frequency_trading': '高频交易',
                'regular_brush_trading': '规律性刷量',
                'wash_trading': '对敲交易',
                'spoofing': '虚假挂单',
                'front_running': '抢先交易',
                'pump_and_dump': '拉高出货',
                'insider_trading': '内幕交易',
                'layering': '分层交易操纵',
                'quote_stuffing': '大量挂单撤单',
                'momentum_ignition': '趋势诱导',
                'ramping': '拉抬股价',
                'odd_lot_manipulation': '零股操纵',
                'marking_the_close': '盘尾操纵',
                'funding_rate_arbitrage': '资金费率交易',
                'suspected_wash_trading': '对敲交易',
                // 新增区分后的对敲类型
                '同账户对敲': '同账户对敲',
                '跨账户对敲': '跨账户对敲'
            };
            
            // 是否需要进行异常类型名称转换
            const isAnomalyTypeChart = title === '异常类型分布';
            
            // 判断是否是金额类数据(对于contract_distribution)
            const isAmountData = title === '合约交易分布' && 
                Object.values(data).some(value => value > 10000); // 如果有值>10000，可能是金额
            
            // 过滤掉值为0的数据项，并准备图表数据
            let chartData = [];
            let isLargeData = false;
            
            if (isAmountData) {
                
                // 对于金额类数据，格式化显示并限制显示的项目数
                const sortedEntries = Object.entries(data)
                    .filter(([key, value]) => value > 0)
                    .sort((a, b) => b[1] - a[1]);
                
                // 如果数据项太多，只保留前7项，其余归为"其他"
                if (sortedEntries.length > 7) {
                    isLargeData = true;
                    const topEntries = sortedEntries.slice(0, 7);
                    const otherEntries = sortedEntries.slice(7);
                    const otherSum = otherEntries.reduce((sum, entry) => sum + entry[1], 0);
                    
                    chartData = topEntries.map(([name, value]) => ({
                        name: name,
                        value: value,
                        // 金额格式化，如 1,234,567.89 USDT
                        formattedValue: new Intl.NumberFormat('zh-CN').format(Math.round(value))
                    }));
                    
                    if (otherSum > 0) {
                        chartData.push({
                            name: '其他',
                            value: otherSum,
                            formattedValue: new Intl.NumberFormat('zh-CN').format(Math.round(otherSum))
                        });
                    }
                } else {
                    chartData = sortedEntries.map(([name, value]) => ({
                        name: name,
                        value: value,
                        formattedValue: new Intl.NumberFormat('zh-CN').format(Math.round(value))
                    }));
                }
            } else if (isAnomalyTypeChart) {
                // 对于异常类型分布图表，进行英文到中文的转换
                
                chartData = Object.entries(data)
                    .filter(([key, value]) => value > 0)
                    .map(([name, value]) => ({
                        // 如果有映射则使用中文名，否则保留原名
                        name: anomalyTypeMapping[name] || name,
                        value: value,
                        originalName: name
                    }));
            } else {
                // 对于其他计数类数据，正常处理
                chartData = Object.entries(data)
                    .filter(([key, value]) => value > 0)
                    .map(([name, value]) => ({ name, value }));
            }
            
            // 如果过滤后没有数据，显示提示信息
            if (chartData.length === 0) {
                console.warn(`过滤后图表数据为空: ${title}`);
                element.innerHTML = `<div class="alert alert-warning">没有${title}数据可显示</div>`;
                return;
            }
            
            // 根据数据类型定制tooltip formatter
            let tooltipFormatter;
            if (isAmountData) {
                tooltipFormatter = function(params) {
                    const data = params.data;
                    return `${params.seriesName}<br/>${data.name}: ${data.formattedValue} USDT (${params.percent}%)`;
                };
            } else if (isAnomalyTypeChart) {
                tooltipFormatter = function(params) {
                    const data = params.data;
                    return `${params.seriesName}<br/>${data.name}: ${data.value} 个 (${params.percent}%)`;
                };
            } else {
                tooltipFormatter = '{a} <br/>{b}: {c} ({d}%)';
            }
            
            const option = {
                title: { text: title, left: 'center' },
                tooltip: { 
                    trigger: 'item', 
                    formatter: tooltipFormatter
                },
                legend: { 
                    show: false  // 隐藏图例，清理左上角的中文介绍
                },
                series: [{
                    name: title,
                    type: 'pie',
                    radius: '70%',
                    data: chartData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        show: true,
                        formatter: function(params) {
                            if (isAmountData) {
                                const data = params.data;
                                // 对于金额，只显示名称和百分比，不显示具体数值避免拥挤
                                return `${data.name}\n${params.percent}%`;
                            }
                            return `${params.name}: ${params.value} (${params.percent}%)`;
                        }
                    }
                }]
            };
            
            const chart = echarts.init(element);
            chart.setOption(option);
            window[chartInstanceKey] = chart; // 存储实例
            
            // 添加窗口大小变化时的自适应调整
            window.addEventListener('resize', () => {
                if (chart && !chart.isDisposed()) {
                    chart.resize();
                }
            });
        } catch (error) {
            console.error(`渲染图表 ${title} 失败:`, error);
            element.innerHTML = `<div class="alert alert-danger">图表渲染失败: ${error.message}</div>`;
        }
    }

    function populateTable(tbodyElement, dataArray, columns, includeDetailsButton = false, columnMapping = null, isPaginatedData = false) {
        
        // 列名映射为中文
        const columnTitleMap = {
            'memberId': '用户ID',
            'digital_id': '数字ID',
            'contractName': '合约名称',
            'detection_type': '异常类型',
            'timeRange': '时间范围',
            'abnormal_volume': '异常交易量',
            'risk_level': '风险等级',
            'data_completeness': '数据完整性',
            'transaction_frequency': '交易频率',
            'reason': '异常原因',
            'abnormal_cause': '异常原因'
        };
        
        // 列宽度设置（百分比）
        const columnWidths = {
            'memberId': '9%',              // 用户ID
            'contractName': '8%',          // 合约名称
            'detection_type': '8%',        // 异常类型
            'timeRange': '0.1%',           // 时间范围（隐藏）
            'abnormal_volume': '9%',       // 异常交易量
            'risk_level': '6%',            // 风险等级
            'data_completeness': '8%',     // 数据完整性
            'transaction_frequency': '8%', // 交易频率
            'abnormal_cause': '43.9%'      // 异常原因（调整后的宽度）
        };
        
        // 分页设置 - 区分客户端分页和服务器端分页
        const itemsPerPage = 30;
        let currentPage = 1;
        let totalPages = Math.ceil(dataArray.length / itemsPerPage);
        
        // 如果是分页数据且有全局分页信息，使用服务器端分页
        const useServerPagination = window.currentContractPagination && window.currentContractPagination.total_pages > 1;
        if (useServerPagination) {
            totalPages = window.currentContractPagination.total_pages;
            currentPage = window.currentContractPagination.page;
            
        } else {

        }
        
        // 检查参数
        if (!tbodyElement) {
            console.error('表格体元素不存在');
            return;
        }
        
        tbodyElement.innerHTML = ''; // 清空现有行
        
        // 检查数据
        if (!dataArray || dataArray.length === 0) {
            const numCols = columns.length + (includeDetailsButton ? 1 : 0);
            tbodyElement.innerHTML = `<tr><td colspan="${numCols}" class="text-center">没有数据</td></tr>`;

            return;
        }
        
        try {
            // 可以通过检查parentElement中是否已有标题行来决定是否需要添加标题
            const tableElement = tbodyElement.closest('table');
            
            // 为表格添加样式
            if (tableElement) {
                tableElement.classList.add('table-striped', 'table-hover');
                tableElement.style.tableLayout = 'fixed';
                tableElement.style.width = '100%';
                tableElement.style.borderCollapse = 'collapse'; // 确保边框合并
                tableElement.style.fontSize = '0.95rem'; // 表格整体使用稍小的字体
            }
            
            const existingThead = tableElement ? tableElement.querySelector('thead') : null;
            
            // 如果表格没有标题行，添加一个
            if (tableElement && !existingThead) {
                const thead = document.createElement('thead');
                thead.style.backgroundColor = '#f8f9fa';
                const headerRow = document.createElement('tr');
                
                // 为每列添加标题
                columns.forEach(colKey => {
                    const th = document.createElement('th');
                    th.textContent = columnTitleMap[colKey] || colKey;
                    
                    // 设置列宽
                    if (columnWidths[colKey]) {
                        th.style.width = columnWidths[colKey];
                    }
                    
                    // 设置标题样式
                    th.style.padding = '8px 3px'; // 减少水平内边距
                    th.style.verticalAlign = 'middle';
                    th.style.fontWeight = 'bold';
                    th.style.fontSize = '0.95rem'; // 标题使用稍小的字体
                    th.style.whiteSpace = 'nowrap'; // 标题不换行
                    th.style.overflow = 'hidden';
                    th.style.textOverflow = 'ellipsis';
                    
                    // 异常交易量标题右对齐
                    if (colKey === 'abnormal_volume') {
                        th.style.textAlign = 'left'; // 标题也改为左对齐
                        th.style.paddingRight = '0px';
                        th.style.paddingLeft = '5px'; // 添加左内边距
                    } else if (colKey === 'timeRange') {
                        th.style.textAlign = 'left'; // 左对齐
                        th.style.paddingLeft = '0px'; // 移除左内边距
                        th.style.marginLeft = '-150px'; // 极大增加负边距(从-100px增加到-150px)
                        th.style.position = 'relative'; // 添加相对定位
                        th.style.fontSize = '0.85rem'; // 更小的字体
                        th.style.zIndex = '1'; // 确保时间范围显示在上层
                    } else if (colKey === 'detection_type') {
                        th.style.paddingRight = '0px'; // 完全移除右内边距
                        th.style.marginRight = '-70px'; // 增加右边负边距(从-50px增加到-70px)
                        th.style.position = 'relative';
                    }
                    
                    headerRow.appendChild(th);
                });
                
                // 如果需要添加详情按钮列
                if (includeDetailsButton) {
                    const th = document.createElement('th');
                    th.textContent = '操作';
                    th.style.width = '8%';
                    headerRow.appendChild(th);
                }
                
                thead.appendChild(headerRow);
                tableElement.insertBefore(thead, tbodyElement);
            }
            
            // 创建分页函数
            function renderPage(pageNum) {
                currentPage = pageNum;
                tbodyElement.innerHTML = ''; // 清空现有内容
                
                let pageData;
                let startIndex = 0; // 初始化 startIndex 变量
                
                if (useServerPagination && pageNum !== window.currentContractPagination.page) {
                    // 服务器端分页 - 需要重新请求数据
                    
                    // 显示加载状态
                    tbodyElement.innerHTML = `<tr><td colspan="${columns.length + (includeDetailsButton ? 1 : 0)}" class="text-center">正在加载第${pageNum}页数据...</td></tr>`;
                    
                    // 异步加载新页面数据
                    loadTaskResultWithPagination(window.currentContractTaskId, pageNum);
                    return; // 直接返回，不继续处理当前数据
                } else if (useServerPagination && pageNum === window.currentContractPagination.page) {
                    // 服务器端分页，但是当前页数据，直接显示
                    pageData = dataArray; // 服务器返回的就是当前页的完整数据
                    startIndex = (pageNum - 1) * itemsPerPage; // 用于显示行号
                } else {
                    // 客户端分页
                    startIndex = (pageNum - 1) * itemsPerPage;
                    const endIndex = Math.min(startIndex + itemsPerPage, dataArray.length);
                    pageData = dataArray.slice(startIndex, endIndex);
                }
                
                // 填充当前页的数据
                pageData.forEach((item, index) => {
                    const row = tbodyElement.insertRow();
                    
                    // 为偶数行添加不同背景色
                    if (index % 2 === 1) {
                        row.style.backgroundColor = '#f9f9f9';
                    }
                    
                    // 全局行高设置，使整个表格更紧凑
                    row.style.lineHeight = '1.3';
                    
                    // 添加每一列的数据
                    columns.forEach(colKey => {
                        const cell = row.insertCell();
                        cell.style.padding = '6px 3px'; // 减小单元格内边距
                        cell.style.verticalAlign = 'middle';
                        
                        // 针对用户ID和合约名称设置更紧凑的样式
                        if (colKey === 'memberId' || colKey === 'contractName') {
                            cell.style.fontSize = '0.95rem';
                            cell.style.whiteSpace = 'nowrap';
                            cell.style.overflow = 'hidden';
                            cell.style.textOverflow = 'ellipsis';
                            
                            // 为用户ID添加点击复制功能
                            if (colKey === 'memberId') {
                                cell.style.cursor = 'pointer';
                                cell.style.color = '#0066cc';
                                cell.style.textDecoration = 'underline';
                                cell.title = '点击复制完整用户ID';
                            }
                        }
                        
                        // 针对异常类型设置样式，减少右边距使时间范围更靠近
                        if (colKey === 'detection_type') {
                            cell.style.fontSize = '0.95rem';
                            cell.style.paddingRight = '0px'; // 移除右内边距
                            cell.style.marginRight = '-70px'; // 增加右边负边距(从-50px增加到-70px)
                            cell.style.whiteSpace = 'nowrap';
                            cell.style.textAlign = 'left';
                            cell.style.position = 'relative';
                        }
                        
                        // 针对时间范围列使用更小的字体
                        if (colKey === 'timeRange') {
                            cell.style.fontSize = '0.85rem'; // 使用更小的字体
                            cell.style.whiteSpace = 'nowrap';
                            cell.style.textAlign = 'left'; // 改为左对齐，靠近异常类型
                            cell.style.padding = '6px 0px'; // 减小内边距
                            cell.style.paddingLeft = '0px'; // 完全移除左内边距
                            // 大幅增加负的左边距，拉近与前一列的距离
                            cell.style.marginLeft = '-180px'; // 极大增加负边距(从-120px增加到-180px)
                            cell.style.position = 'relative'; // 添加相对定位
                            cell.style.zIndex = '2'; // 确保时间范围显示在上层(增加优先级)
                        }
                        
                        // 针对异常交易量设置紧凑样式
                        if (colKey === 'abnormal_volume') {
                            cell.style.fontSize = '0.95rem';
                            cell.style.whiteSpace = 'nowrap';
                            cell.style.textAlign = 'left'; // 改为左对齐
                            cell.style.paddingRight = '0px'; 
                            cell.style.paddingLeft = '5px'; // 添加左内边距
                        }
                        
                        // 如果有字段映射，尝试找到匹配的字段
                        let value = undefined;
                        if (columnMapping && columnMapping[colKey]) {
                            // 尝试所有可能的字段名
                            for (const fieldName of columnMapping[colKey]) {
                                if (item[fieldName] !== undefined) {
                                    value = item[fieldName];
                                    break;
                                }
                            }
                        } else {
                            value = item[colKey];
                        }
                        
                        // 特殊处理不同类型的列
                        if (colKey === 'abnormal_volume' && value !== undefined && value !== null) {
                            // 格式化交易量
                            if (typeof value === 'string' && value.includes('USDT')) {
                                cell.textContent = value;
                            } else {
                                const numValue = parseFloat(value);
                                if (!isNaN(numValue)) {
                                    const formattedValue = new Intl.NumberFormat('zh-CN').format(Math.round(numValue));
                                    cell.textContent = `${formattedValue} USDT`;
                                } else {
                                    cell.textContent = value;
                                }
                            }
                        } else if (colKey === 'timeRange' && value !== undefined && value !== null) {
                            // 检查是否是预处理后的格式（使用|-|分隔符）
                            if (value.includes('|-|')) {
                                const [startDateTime, endDateTime] = value.split('|-|');
                                // 分3行显示格式：时间靠左，横线居中
                                cell.innerHTML = `<div style="line-height: 1.2;">
                                    <div style="text-align: left;">${startDateTime}</div>
                                    <div style="text-align: center;">-</div>
                                    <div style="text-align: left;">${endDateTime}</div>
                                </div>`;
                            } else {
                                // 如果不是预处理格式，直接显示原始值
                                cell.textContent = value;
                            }
                        } else if (colKey === 'risk_level' || colKey === 'severity') {
                            // 设置风险等级的样式
                            let badgeClass = 'badge ';
                            if (value === 'high' || value === '高') {
                                badgeClass += 'bg-danger';
                                value = '高';
                            } else if (value === 'medium' || value === '中') {
                                badgeClass += 'bg-warning text-dark';
                                value = '中';
                            } else if (value === 'low' || value === '低') {
                                badgeClass += 'bg-success';
                                value = '低';
                            } else {
                                badgeClass += 'bg-secondary';
                            }
                            
                            // 修改风险等级显示方式，改为靠左
                            cell.style.textAlign = 'left'; // 单元格内容靠左
                            cell.style.verticalAlign = 'middle'; // 垂直居中
                            cell.style.paddingLeft = '10px'; // 增加左侧内边距
                            
                            const badge = document.createElement('span');
                            badge.className = badgeClass;
                            badge.style.fontSize = '0.9rem'; // 增大字体大小
                            badge.style.padding = '5px 12px'; // 增大内边距
                            badge.style.minWidth = '50px'; // 设置最小宽度
                            badge.style.display = 'inline-block'; // 使宽度生效
                            badge.style.textAlign = 'center'; // 文本在标签内部仍然居中
                            badge.textContent = value !== undefined && value !== null ? value : '未知';
                            cell.appendChild(badge);
                        } else if ((colKey === 'reason' || colKey === 'abnormal_cause') && value) {
                            // 限制原因字段的长度
                            const maxLength = 70; // 增加显示长度，从50增加到70
                            
                            // 优化异常原因单元格样式
                            cell.style.textAlign = 'left';
                            cell.style.verticalAlign = 'middle';
                            cell.style.paddingLeft = '3px'; // 进一步减少左内边距
                            cell.style.paddingRight = '3px'; // 减少右内边距
                            cell.style.fontSize = '0.95rem'; // 稍微减小字体
                            cell.style.lineHeight = '1.3'; // 减小行高，使文本更紧凑
                            
                            if (value.length > maxLength) {
                                cell.textContent = value.substring(0, maxLength) + '...';
                                // 添加完整内容的提示框
                                cell.title = value;
                                cell.style.cursor = 'pointer';
                            } else {
                                cell.textContent = value;
                            }
                        } else {
                            // 特殊处理用户ID：显示时格式化，复制时使用完整值
                            if (colKey === 'memberId' && value !== undefined && value !== null && value !== '-') {
                                // 获取完整的用户ID（优先使用_originalMemberId，否则使用value）
                                const fullMemberId = item._originalMemberId || value;
                                
                                // 格式化显示：如果用户ID长度大于10，显示前4后6位
                                let displayValue = fullMemberId;
                                if (fullMemberId && fullMemberId.length > 10) {
                                    displayValue = `${fullMemberId.substring(0, 4)}****${fullMemberId.substring(fullMemberId.length - 6)}`;
                                }
                                
                                cell.textContent = displayValue;
                                
                                // 将完整的用户ID存储在data属性中供复制使用
                                cell.setAttribute('data-full-member-id', fullMemberId);
                                
                                cell.addEventListener('click', function() {
                                    // 从data属性中获取完整的用户ID进行复制
                                    const fullId = this.getAttribute('data-full-member-id');
                                    copyToClipboard(fullId, '用户ID');
                                });
                            } else {
                                cell.textContent = value !== undefined && value !== null ? value : '-';
                            }
                        }
                        
                        // 检查最后一行的内容是否有问题
                        if (startIndex + index === dataArray.length - 1) {
                            // 确保异常交易量正确显示
                            if (colKey === 'abnormal_volume' && (!value || value === '-')) {
                                // 尝试从abnormal_cause中提取交易量
                                const causeValue = item.abnormal_cause || item.reason;
                                if (causeValue && causeValue.includes('总量')) {
                                    const match = causeValue.match(/总量\s*([\d,\.]+)\s*USDT/);
                                    if (match && match[1]) {
                                        cell.textContent = `${match[1]} USDT`;
                                    }
                                }
                            }
                        }
                    });
                    
                    // 如果需要，添加详情按钮
                    if (includeDetailsButton) {
                        const cell = row.insertCell();
                        cell.style.textAlign = 'center';
                        const btn = document.createElement('button');
                        btn.className = 'btn btn-sm btn-outline-info';
                        btn.innerHTML = '<i class="bi bi-eye"></i> 详情';
                        btn.onclick = () => showDetailModal(item); 
                        cell.appendChild(btn);
                    }
                });
                
                // 更新分页控件
                updatePaginationControls();
            }
            
            // 创建分页控件
            function createPaginationControls() {
                const table = tableElement;
                if (!table || totalPages <= 1) return; // 不需要分页
                
                // 查找现有分页控件，如果没有则创建
                let paginationDiv = table.nextElementSibling;
                if (!paginationDiv || !paginationDiv.classList.contains('pagination-container')) {
                    paginationDiv = document.createElement('div');
                    paginationDiv.className = 'pagination-container';
                    paginationDiv.style.marginTop = '15px';
                    paginationDiv.style.textAlign = 'center';
                    table.parentNode.insertBefore(paginationDiv, table.nextSibling);
                } else {
                    paginationDiv.innerHTML = ''; // 清空现有控件
                }
                
                // 创建分页导航
                const nav = document.createElement('nav');
                const ul = document.createElement('ul');
                ul.className = 'pagination';
                
                // 创建"上一页"按钮
                const prevLi = document.createElement('li');
                prevLi.className = 'page-item';
                prevLi.id = 'prevPageBtn';
                const prevLink = document.createElement('a');
                prevLink.className = 'page-link';
                prevLink.href = '#';
                prevLink.textContent = '上一页';
                prevLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    if (currentPage > 1) {
                        renderPage(currentPage - 1);
                    }
                });
                prevLi.appendChild(prevLink);
                ul.appendChild(prevLi);
                
                // 创建页码按钮
                const maxVisiblePages = 5; // 最大可见页数
                let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
                
                // 调整起始页，确保显示足够的页码
                if (endPage - startPage + 1 < maxVisiblePages) {
                    startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }
                
                // 如果是第一页，添加省略号前的页码
                if (startPage > 1) {
                    const firstLi = document.createElement('li');
                    firstLi.className = 'page-item';
                    const firstLink = document.createElement('a');
                    firstLink.className = 'page-link';
                    firstLink.href = '#';
                    firstLink.textContent = '1';
                    firstLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        renderPage(1);
                    });
                    firstLi.appendChild(firstLink);
                    ul.appendChild(firstLi);
                    
                    if (startPage > 2) {
                        const ellipsisLi = document.createElement('li');
                        ellipsisLi.className = 'page-item disabled';
                        const ellipsisSpan = document.createElement('span');
                        ellipsisSpan.className = 'page-link';
                        ellipsisSpan.textContent = '...';
                        ellipsisLi.appendChild(ellipsisSpan);
                        ul.appendChild(ellipsisLi);
                    }
                }
                
                // 添加中间的页码
                for (let i = startPage; i <= endPage; i++) {
                    const pageLi = document.createElement('li');
                    pageLi.className = 'page-item';
                    if (i === currentPage) {
                        pageLi.classList.add('active');
                    }
                    const pageLink = document.createElement('a');
                    pageLink.className = 'page-link';
                    pageLink.href = '#';
                    pageLink.textContent = i.toString();
                    pageLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        renderPage(i);
                    });
                    pageLi.appendChild(pageLink);
                    ul.appendChild(pageLi);
                }
                
                // 如果不是最后一页，添加省略号后的页码
                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        const ellipsisLi = document.createElement('li');
                        ellipsisLi.className = 'page-item disabled';
                        const ellipsisSpan = document.createElement('span');
                        ellipsisSpan.className = 'page-link';
                        ellipsisSpan.textContent = '...';
                        ellipsisLi.appendChild(ellipsisSpan);
                        ul.appendChild(ellipsisLi);
                    }
                    
                    const lastLi = document.createElement('li');
                    lastLi.className = 'page-item';
                    const lastLink = document.createElement('a');
                    lastLink.className = 'page-link';
                    lastLink.href = '#';
                    lastLink.textContent = totalPages.toString();
                    lastLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        renderPage(totalPages);
                    });
                    lastLi.appendChild(lastLink);
                    ul.appendChild(lastLi);
                }
                
                // 创建"下一页"按钮
                const nextLi = document.createElement('li');
                nextLi.className = 'page-item';
                nextLi.id = 'nextPageBtn';
                const nextLink = document.createElement('a');
                nextLink.className = 'page-link';
                nextLink.href = '#';
                nextLink.textContent = '下一页';
                nextLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    if (currentPage < totalPages) {
                        renderPage(currentPage + 1);
                    }
                });
                nextLi.appendChild(nextLink);
                ul.appendChild(nextLi);
                
                nav.appendChild(ul);
                paginationDiv.appendChild(nav);
                
                // 添加页码信息
                const pageInfo = document.createElement('div');
                pageInfo.style.marginTop = '10px';
                const totalRecords = useServerPagination ? 
                    window.currentContractPagination.total_records : 
                    dataArray.length;
                pageInfo.innerHTML = `<span>共 ${totalRecords} 条数据，${totalPages} 页${useServerPagination ? ' (服务器分页)' : ''}</span>`;
                paginationDiv.appendChild(pageInfo);
            }
            
            // 更新分页控件状态
            function updatePaginationControls() {
                const prevBtn = document.getElementById('prevPageBtn');
                const nextBtn = document.getElementById('nextPageBtn');
                
                if (prevBtn) {
                    if (currentPage === 1) {
                        prevBtn.classList.add('disabled');
                    } else {
                        prevBtn.classList.remove('disabled');
                    }
                }
                
                if (nextBtn) {
                    if (currentPage === totalPages) {
                        nextBtn.classList.add('disabled');
                    } else {
                        nextBtn.classList.remove('disabled');
                    }
                }
                
                // 更新活动页码
                const pageItems = document.querySelectorAll('.pagination .page-item');
                pageItems.forEach(item => {
                    if (!item.id) { // 跳过上一页和下一页按钮
                        const pageLink = item.querySelector('.page-link');
                        if (pageLink && !pageLink.textContent.includes('...')) {
                            const pageNum = parseInt(pageLink.textContent);
                            if (pageNum === currentPage) {
                                item.classList.add('active');
                            } else {
                                item.classList.remove('active');
                            }
                        }
                    }
                });
            }
            
            // 创建分页控件
            createPaginationControls();
            
            // 渲染当前页，如果是服务器分页则渲染服务器指定的页面，否则渲染第一页
            if (useServerPagination && window.currentContractPagination && window.currentContractPagination.page) {
                currentPage = window.currentContractPagination.page;
                // 对于服务器端分页，数据已经是当前页的数据，直接渲染即可
                if (isPaginatedData) {
                    // 如果是分页数据，直接渲染当前页，不需要重新加载
                    renderPage(currentPage);
                } else {
                    // 首次加载，渲染第一页
                    renderPage(1);
                }
            } else {
                // 客户端分页，渲染第一页
                renderPage(1);
            }
            
        } catch (error) {
            console.error('填充表格时发生错误:', error);
            tbodyElement.innerHTML = `<tr><td colspan="${columns.length + (includeDetailsButton ? 1 : 0)}" class="text-center text-danger">表格渲染错误: ${error.message}</td></tr>`;
        }
    }
    
    function showDetailModal(itemData) {
        if (detailModalInstance && detailModalBody) {
            // 生成用户友好的详情内容
            const detailContent = generateDetailContent(itemData);
            detailModalBody.innerHTML = detailContent;
            detailModalInstance.show();
        } else {
            console.error('Detail modal instance or body not found.');
        }
    }

    function generateDetailContent(itemData) {
        const detectionType = itemData.detection_type || '未知';
        const memberId = itemData.memberId || itemData.member_id || '未知';
        const contractName = itemData.contractName || itemData.contract_name || '未知';

        let content = `
            <div class="anomaly-details">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-section">
                            <h5><i class="bi bi-info-circle"></i> 基本信息</h5>
                            <div class="detail-item">
                                <label>用户ID:</label>
                                <span class="copyable" onclick="copyToClipboard('${memberId}', '用户ID')">
                                    ${memberId}
                                    <i class="bi bi-clipboard ms-1"></i>
                                </span>
                            </div>
                            <div class="detail-item">
                                <label>合约名称:</label>
                                <span>${contractName}</span>
                            </div>
                            <div class="detail-item">
                                <label>异常类型:</label>
                                <span class="badge bg-warning">${detectionType}</span>
                            </div>
                            <div class="detail-item">
                                <label>风险等级:</label>
                                <span class="badge ${getRiskLevelClass(itemData.severity || itemData.risk_level)}">${itemData.severity || itemData.risk_level || '中'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-section">
                            <h5><i class="bi bi-graph-up"></i> 交易信息</h5>
                            <div class="detail-item">
                                <label>异常交易量:</label>
                                <span>${formatVolume(itemData.abnormal_volume || itemData.abnormalVolume)} USDT</span>
                            </div>
                            <div class="detail-item">
                                <label>时间范围:</label>
                                <span>${itemData.minute || itemData.timeRange || '未知'}</span>
                            </div>
                            <div class="detail-item">
                                <label>异常原因:</label>
                                <span>${itemData.reason || '未提供'}</span>
                            </div>
                        </div>
                    </div>
                </div>
        `;

        // 根据不同的异常类型添加专门信息
        if (detectionType === 'wash_trading' || detectionType === '对敲交易') {
            content += generateWashTradingDetails(itemData);
        } else if (detectionType === 'high_frequency_trading' || detectionType === '高频交易') {
            content += generateHighFrequencyDetails(itemData);
        } else if (detectionType === 'regular_brush_trading' || detectionType === '规律性刷量') {
            content += generateRegularBrushDetails(itemData);
        } else if (detectionType === 'funding_rate_arbitrage' || detectionType === '资金费率交易') {
            content += generateFundingArbitrageDetails(itemData);
        }

        // 添加原始数据折叠区域
        content += `
                <div class="mt-3">
                    <div class="accordion" id="rawDataAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="rawDataHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#rawDataCollapse" aria-expanded="false">
                                    <i class="bi bi-code-square me-2"></i>原始数据 (开发调试用)
                                </button>
                            </h2>
                            <div id="rawDataCollapse" class="accordion-collapse collapse" data-bs-parent="#rawDataAccordion">
                                <div class="accordion-body">
                                    <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto; font-size: 12px;">${JSON.stringify(itemData, null, 2)}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return content;
    }

    function generateWashTradingDetails(itemData) {
        let details = `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="detail-section">
                        <h5><i class="bi bi-arrow-left-right"></i> 对敲交易详情</h5>
        `;

        if (itemData.opponent_member_id) {
            details += `
                        <div class="detail-item">
                            <label>对手方用户ID:</label>
                            <span class="copyable" onclick="copyToClipboard('${itemData.opponent_member_id}', '对手方用户ID')">
                                ${itemData.opponent_member_id}
                                <i class="bi bi-clipboard ms-1"></i>
                            </span>
                        </div>
            `;
        }

        if (itemData.wash_score) {
            details += `
                        <div class="detail-item">
                            <label>对敲评分:</label>
                            <span>${itemData.wash_score}</span>
                        </div>
            `;
        }

        if (itemData.user_a_profit !== undefined && itemData.user_b_profit !== undefined) {
            details += `
                        <div class="detail-item">
                            <label>用户A盈利:</label>
                            <span class="${itemData.user_a_profit >= 0 ? 'text-success' : 'text-danger'}">${formatVolume(itemData.user_a_profit)} USDT</span>
                        </div>
                        <div class="detail-item">
                            <label>用户B盈利:</label>
                            <span class="${itemData.user_b_profit >= 0 ? 'text-success' : 'text-danger'}">${formatVolume(itemData.user_b_profit)} USDT</span>
                        </div>
            `;
        }

        if (itemData.detection_method) {
            details += `
                        <div class="detail-item">
                            <label>检测方法:</label>
                            <span>${itemData.detection_method}</span>
                        </div>
            `;
        }

        details += `
                    </div>
                </div>
            </div>
        `;

        return details;
    }

    function generateHighFrequencyDetails(itemData) {
        let details = `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="detail-section">
                        <h5><i class="bi bi-speedometer2"></i> 高频交易详情</h5>
        `;

        if (itemData.trade_frequency) {
            details += `
                        <div class="detail-item">
                            <label>交易频率:</label>
                            <span>${itemData.trade_frequency} 次/小时</span>
                        </div>
            `;
        }

        if (itemData.total_trades) {
            details += `
                        <div class="detail-item">
                            <label>总交易次数:</label>
                            <span>${itemData.total_trades}</span>
                        </div>
            `;
        }

        if (itemData.avg_interval) {
            details += `
                        <div class="detail-item">
                            <label>平均交易间隔:</label>
                            <span>${itemData.avg_interval} 秒</span>
                        </div>
            `;
        }

        details += `
                    </div>
                </div>
            </div>
        `;

        return details;
    }

    function generateRegularBrushDetails(itemData) {
        let details = `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="detail-section">
                        <h5><i class="bi bi-arrow-repeat"></i> 规律性刷量详情</h5>
        `;

        if (itemData.short_positions_count) {
            details += `
                        <div class="detail-item">
                            <label>短持仓次数:</label>
                            <span>${itemData.short_positions_count}</span>
                        </div>
            `;
        }

        if (itemData.total_duration) {
            details += `
                        <div class="detail-item">
                            <label>总持仓时长:</label>
                            <span>${itemData.total_duration} 秒</span>
                        </div>
            `;
        }

        if (itemData.volume_ratio) {
            details += `
                        <div class="detail-item">
                            <label>交易量比例:</label>
                            <span>${itemData.volume_ratio}</span>
                        </div>
            `;
        }

        details += `
                    </div>
                </div>
            </div>
        `;

        return details;
    }

    function generateFundingArbitrageDetails(itemData) {
        let details = `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="detail-section">
                        <h5><i class="bi bi-currency-exchange"></i> 资金费率套利详情</h5>
        `;

        if (itemData.total_positions) {
            details += `
                        <div class="detail-item">
                            <label>总持仓数:</label>
                            <span>${itemData.total_positions}</span>
                        </div>
            `;
        }

        if (itemData.pattern_matched_positions) {
            details += `
                        <div class="detail-item">
                            <label>匹配模式持仓数:</label>
                            <span>${itemData.pattern_matched_positions}</span>
                        </div>
            `;
        }

        if (itemData.pattern_ratio) {
            details += `
                        <div class="detail-item">
                            <label>模式匹配率:</label>
                            <span>${(itemData.pattern_ratio * 100).toFixed(2)}%</span>
                        </div>
            `;
        }

        if (itemData.contracts_involved && itemData.contracts_involved.length > 0) {
            details += `
                        <div class="detail-item">
                            <label>涉及合约:</label>
                            <span>${itemData.contracts_involved.join(', ')}</span>
                        </div>
            `;
        }

        details += `
                    </div>
                </div>
            </div>
        `;

        return details;
    }

    function getRiskLevelClass(riskLevel) {
        switch(riskLevel) {
            case '高':
            case 'high':
                return 'bg-danger';
            case '中':
            case 'medium':
                return 'bg-warning';
            case '低':
            case 'low':
                return 'bg-success';
            default:
                return 'bg-secondary';
        }
    }

    function formatVolume(volume) {
        if (volume === undefined || volume === null) return '0';
        const numValue = parseFloat(volume);
        if (isNaN(numValue)) return volume;
        return new Intl.NumberFormat('zh-CN').format(Math.round(numValue));
    }
    
            // 分页和搜索功能已在其他模块中实现

    // 测试按钮函数已删除 - 生产环境不需要

    // --- 辅助函数：使用表单提交方式强制页面跳转 ---
    function forceNavigateWithTaskId(taskId) {
        
        // 创建一个临时表单
        const form = document.createElement('form');
        form.method = 'GET';
        form.action = window.location.pathname; // 当前路径
        
        // 添加taskId参数
        const taskIdInput = document.createElement('input');
        taskIdInput.type = 'hidden';
        taskIdInput.name = 'taskId';
        taskIdInput.value = taskId;
        form.appendChild(taskIdInput);
        
        // 添加到文档并提交
        document.body.appendChild(form);
        form.submit();
    }

    // --- 辅助函数：使用window.location.href强制页面跳转并刷新 ---
    function forceReplaceWithTaskId(taskId) {
        try {
            if (!taskId || taskId === 'undefined' || taskId === 'null') {
                showAlert('无法更新URL并刷新：任务ID缺失。', 'danger');
                return;
            }
            
            const baseUrl = window.location.protocol + '//' + 
                            window.location.host + 
                            window.location.pathname;
            
            let newUrl = `${baseUrl}?taskId=${encodeURIComponent(taskId)}`;
            
            window.location.replace(newUrl);
        } catch (e) {
            showAlert(`URL更新并刷新失败: ${e.message}`, 'danger');
            hasInitiatedResultDisplayAndRefresh = false;
        }
    }

    function renderContractBarChart(element, title, data, chartInstanceKey) {
        // 检查element是否存在
        if (!element) {
            console.error(`图表容器元素不存在: ${title}`);
            return;
        }
        
        // 检查数据是否有效并且不为空
        if (!data || Object.keys(data).length === 0) {
            element.innerHTML = `<div class="alert alert-warning">没有${title}数据可显示</div>`;
            return;
        }
        
        try {
            // 如果存在旧图表实例，销毁它
            if (window[chartInstanceKey]) {
                window[chartInstanceKey].dispose();
            }
            
            // 检查echarts是否存在
            if (typeof echarts === 'undefined') {
                console.error('ECharts库未加载');
                element.innerHTML = '<div class="alert alert-danger">图表库未加载</div>';
                return;
            }
            
            // 确认是否为金额数据
            const isAmountData = Object.values(data).some(value => value > 10000);
            
            // 将数据转换为数组并按值排序
            let dataArray = Object.entries(data).map(([name, value]) => {
                return {
                    name: name,
                    value: Number(value) || 0
                };
            });
            
            // 按值大小降序排序
            dataArray = dataArray.sort((a, b) => b.value - a.value);
            
            let hasOthers = false;
            let othersItem = null;
            let othersValue = 0;
            
            // 查找"others/usdt"项并从数组中移除
            const othersIndex = dataArray.findIndex(item => item.name.toLowerCase() === 'others/usdt');
            if (othersIndex !== -1) {
                hasOthers = true;
                othersItem = dataArray[othersIndex];
                dataArray.splice(othersIndex, 1);
            }
            
            // 如果数据超过10项，保留前10项，其余合并到"others/usdt"
            let topItems = [];
            if (dataArray.length > 10) {
                // 获取前10项
                topItems = dataArray.slice(0, 10);
                
                // 计算剩余项总和
                for (let i = 10; i < dataArray.length; i++) {
                    othersValue += dataArray[i].value;
                }
                
                // 将原有的"others/usdt"值加到合计中
                if (hasOthers) {
                    othersValue += othersItem.value;
                }
                
                // 创建或更新"others/usdt"项
                if (othersValue > 0) {
                    othersItem = {
                        name: 'others/usdt',
                        value: othersValue
                    };
                }
            } else {
                // 如果数据少于或等于10项，全部显示
                topItems = dataArray;
            }
            
            // 添加"others/usdt"在最后（如果有值）
            if ((hasOthers || othersValue > 0) && othersItem) {
                topItems.push(othersItem);
            }
            
            // 生成图表的X轴和Y轴数据
            const names = topItems.map(item => item.name);
            const values = topItems.map(item => item.value);
            
            // 计算总值用于百分比
            const totalValue = values.reduce((sum, val) => sum + val, 0);
            
            // 定义一组漂亮的颜色，用于不同币种
            const colorPalette = [
                '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
                '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#1d4e8f'
            ];
            
            // 设置Bar Chart的配置
            const option = {
                title: {
                    text: title,
                    left: 'center',
                    top: 10
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        const item = params[0];
                        const value = item.value;
                        const formattedValue = isAmountData ? 
                            new Intl.NumberFormat('zh-CN').format(Math.round(value)) + ' USDT' : 
                            `${value} 次`;
                        const percentage = ((value / totalValue) * 100).toFixed(2);
                        return `${item.name}<br/>${formattedValue}<br/>占比: ${percentage}%`;
                    }
                },
                grid: {
                    left: '5%',
                    right: '5%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: function(value) {
                            if (value >= 1000000) {
                                return (value / 1000000).toFixed(1) + 'M';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(1) + 'K';
                            }
                            return value;
                        }
                    }
                },
                yAxis: {
                    type: 'category',
                    data: names,
                    axisLabel: {
                        interval: 0,
                        rotate: 0,
                        formatter: function(value) {
                            // 如果币种名称太长，截断显示
                            if (value.length > 10) {
                                return value.substring(0, 8) + '...';
                            }
                            return value;
                        }
                    }
                },
                series: [
                    {
                        name: title,
                        type: 'bar',
                        data: values.map((value, index) => {
                            const percentage = ((value / totalValue) * 100).toFixed(2);
                            return {
                                value: value,
                                itemStyle: {
                                    // 为"others/usdt"使用灰色，其他币种使用彩色调色板
                                    color: names[index].toLowerCase() === 'others/usdt' ? 
                                        '#CCCCCC' : 
                                        colorPalette[index % colorPalette.length]
                                },
                                label: {
                                    show: true,
                                    position: 'right',
                                    formatter: function() {
                                        return percentage + '%';
                                    },
                                    color: '#000',
                                    fontSize: 12,
                                    fontWeight: 'bold'
                                }
                            };
                        }),
                        barWidth: '60%', // 设置条形宽度适中
                        showBackground: true, // 显示背景
                        backgroundStyle: {
                            color: 'rgba(220, 220, 220, 0.2)' // 设置浅灰色背景
                        }
                    }
                ]
            };
            
            // 创建图表实例
            const chart = echarts.init(element);
            chart.setOption(option);
            
            // 存储图表实例，便于后续操作
            window[chartInstanceKey] = chart;
            
            // 添加窗口大小变化时的自适应调整
            window.addEventListener('resize', () => {
                if (chart && !chart.isDisposed()) {
                    chart.resize();
                }
            });
        } catch (error) {
            console.error(`渲染条形图 ${title} 失败:`, error);
            element.innerHTML = `<div class="alert alert-danger">条形图渲染失败: ${error.message}</div>`;
        }
    }

    // ==================== 批量查询功能 ====================

    // 批量查询按钮事件
    const batchQueryBtn = document.getElementById('batchQueryBtn');
    const clearBatchInputBtn = document.getElementById('clearBatchInputBtn');
    const batchMemberIds = document.getElementById('batchMemberIds');

    if (batchQueryBtn) {
        batchQueryBtn.addEventListener('click', handleBatchQuery);
    }

    if (clearBatchInputBtn) {
        clearBatchInputBtn.addEventListener('click', () => {
            batchMemberIds.value = '';
            showBatchAlert('输入已清空', 'info');
        });
    }

    function handleBatchQuery() {
        const memberIdsText = batchMemberIds.value.trim();

        if (!memberIdsText) {
            showBatchAlert('请输入用户ID', 'warning');
            return;
        }

        // 解析用户ID列表
        const memberIds = memberIdsText.split('\n')
            .map(id => id.trim())
            .filter(id => id.length > 0);

        if (memberIds.length === 0) {
            showBatchAlert('请输入有效的用户ID', 'warning');
            return;
        }

        if (memberIds.length > 5000) {
            showBatchAlert('一次最多查询5000个用户ID', 'warning');
            return;
        }

        // 禁用按钮，显示加载状态
        batchQueryBtn.disabled = true;
        batchQueryBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 查询中...';

        showBatchAlert(`正在查询 ${memberIds.length} 个用户的异常金额数据...`, 'info');

        // 发送批量查询请求
        fetch('/api/contract/batch-query', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                member_ids: memberIds
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(err => { throw new Error(err.error || '查询失败'); });
            }
            return response.json();
        })
        .then(data => {
            console.log('批量查询结果:', data);

            if (data.download_url) {
                // 直接下载模式
                downloadFile(data.download_url);
                showBatchAlert(`批量查询完成！共查询 ${data.total_users} 个用户，找到 ${data.results_count} 条异常记录。文件下载已开始。`, 'success');
            } else {
                showBatchAlert('查询完成，但未获取到下载链接', 'warning');
            }
        })
        .catch(error => {
            console.error('批量查询失败:', error);
            showBatchAlert(`批量查询失败: ${error.message}`, 'danger');
        })
        .finally(() => {
            // 恢复按钮状态
            batchQueryBtn.disabled = false;
            batchQueryBtn.innerHTML = '<i class="bi bi-search"></i> 批量查询';
        });
    }

    function downloadFile(url) {
        const link = document.createElement('a');
        link.href = url;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    function showBatchAlert(message, type) {
        const alertDiv = document.getElementById('batchQueryAlert');
        if (alertDiv) {
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.classList.remove('d-none');

            // 自动隐藏成功和信息提示
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    alertDiv.classList.add('d-none');
                }, 5000);
            }
        }
    }

    // 🔧 新增：显示交易对详情的模态框功能
    function showTradeDetailModal(item) {
        if (!item.trade_pair_detail) {
            showAlert('暂无详细交易信息', 'warning');
            return;
        }

        const detail = item.trade_pair_detail;
        const modalContent = `
            <div class="trade-detail-modal">
                <h5>交易对详情</h5>
                <div class="row">
                    <div class="col-md-6">
                        <h6>用户A (${detail.user_a?.member_id || 'N/A'})</h6>
                        <p><strong>开仓:</strong> ${detail.user_a?.open_time || 'N/A'} | ${detail.user_a?.open_amount || 0} USDT</p>
                        <p><strong>平仓:</strong> ${detail.user_a?.close_time || 'N/A'} | ${detail.user_a?.close_amount || 0} USDT</p>
                        <p><strong>盈亏:</strong> <span class="${(detail.user_a?.profit || 0) >= 0 ? 'text-success' : 'text-danger'}">${detail.user_a?.profit || 0} USDT</span></p>
                    </div>
                    <div class="col-md-6">
                        <h6>用户B (${detail.user_b?.member_id || 'N/A'})</h6>
                        <p><strong>开仓:</strong> ${detail.user_b?.open_time || 'N/A'} | ${detail.user_b?.open_amount || 0} USDT</p>
                        <p><strong>平仓:</strong> ${detail.user_b?.close_time || 'N/A'} | ${detail.user_b?.close_amount || 0} USDT</p>
                        <p><strong>盈亏:</strong> <span class="${(detail.user_b?.profit || 0) >= 0 ? 'text-success' : 'text-danger'}">${detail.user_b?.profit || 0} USDT</span></p>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>风险评分详情</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>盈亏对冲评分:</strong> <span class="badge bg-primary">${((item.profit_hedge_score || 0) * 100).toFixed(1)}%</span></p>
                            <p><strong>综合对敲评分:</strong> <span class="badge bg-warning">${((item.wash_score || 0) * 100).toFixed(1)}%</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>总交易金额:</strong> ${detail.total_amount || 0} USDT</p>
                            <p><strong>净盈亏:</strong> <span class="${(detail.net_profit || 0) >= 0 ? 'text-success' : 'text-danger'}">${detail.net_profit || 0} USDT</span></p>
                        </div>
                    </div>
                    ${detail.time_gaps ? `
                    <div class="mt-2">
                        <h6>时间差分析</h6>
                        <p><strong>开仓时间差:</strong> ${detail.time_gaps.open_gap_seconds || 0} 秒</p>
                        <p><strong>平仓时间差:</strong> ${detail.time_gaps.close_gap_seconds || 0} 秒</p>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;

        // 显示模态框
        showDetailModal('交易对详情', modalContent);
    }

    // 🔧 通用详情模态框显示函数
    function showDetailModal(title, content) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="tradeDetailModal" tabindex="-1" aria-labelledby="tradeDetailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="tradeDetailModalLabel">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('tradeDetailModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('tradeDetailModal'));
        modal.show();

        // 模态框关闭后清理
        document.getElementById('tradeDetailModal').addEventListener('hidden.bs.modal', function () {
            this.remove();
        });
    }

    // 🔧 将函数暴露到全局作用域，供表格按钮调用
    window.showTradeDetailModal = showTradeDetailModal;
});