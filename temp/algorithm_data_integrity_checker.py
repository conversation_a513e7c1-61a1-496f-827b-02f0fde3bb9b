#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对敲和高频算法数据完整性检测工具
检测算法获取的数据是否完整，包括数据质量、完整性、一致性等方面
"""

import sys
import os
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from backend.database.duckdb_manager import DuckDBManager as DatabaseManager
    from backend.modules.contract_risk_analysis.optimizers.position_based_optimizer import PositionBasedOptimizer
    from backend.modules.contract_risk_analysis.services.incremental_analyzer import IncrementalAnalyzer
    from backend.modules.contract_risk_analysis.models.complete_position import CompletePosition
except ImportError as e:
    print(f"导入模块失败: {e}")
    print(f"项目根目录: {project_root}")
    print(f"Python路径: {sys.path}")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('temp/algorithm_data_integrity_check.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AlgorithmDataIntegrityChecker:
    """算法数据完整性检测器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.integrity_report = {
            'check_time': datetime.now().isoformat(),
            'data_sources': {},
            'algorithm_data': {},
            'integrity_issues': [],
            'completeness_metrics': {},
            'recommendations': []
        }
    
    def run_comprehensive_check(self) -> Dict[str, Any]:
        """运行综合数据完整性检查"""
        logger.info("========== 开始算法数据完整性检查 ==========")
        
        try:
            # 1. 检查原始数据源完整性
            self._check_raw_data_sources()
            
            # 2. 检查完整订单构建过程
            self._check_complete_position_building()
            
            # 3. 检查对敲算法数据获取
            self._check_wash_trading_data_integrity()
            
            # 4. 检查高频算法数据获取
            self._check_high_frequency_data_integrity()
            
            # 5. 检查数据传递链路完整性
            self._check_data_pipeline_integrity()
            
            # 6. 生成完整性评估报告
            self._generate_integrity_assessment()
            
            logger.info("========== 算法数据完整性检查完成 ==========")
            return self.integrity_report
            
        except Exception as e:
            logger.error(f"数据完整性检查失败: {str(e)}")
            self.integrity_report['integrity_issues'].append(f"检查过程出错: {str(e)}")
            return self.integrity_report
    
    def _check_raw_data_sources(self):
        """检查原始数据源完整性"""
        logger.info("检查原始数据源完整性...")
        
        try:
            # 检查position_analysis表数据
            position_sql = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT member_id) as unique_users,
                COUNT(DISTINCT contract_name) as unique_contracts,
                MIN(create_time) as earliest_time,
                MAX(create_time) as latest_time,
                COUNT(CASE WHEN side IN (1, 3) THEN 1 END) as open_trades,
                COUNT(CASE WHEN side IN (2, 4) THEN 1 END) as close_trades
            FROM position_analysis 
            WHERE create_time >= datetime('now', '-7 days')
            """
            
            position_stats = self.db_manager.execute_query(position_sql)
            if position_stats:
                stats = position_stats[0]
                self.integrity_report['data_sources']['position_analysis'] = {
                    'total_records': stats[0],
                    'unique_users': stats[1],
                    'unique_contracts': stats[2],
                    'time_range': f"{stats[3]} to {stats[4]}",
                    'open_trades': stats[5],
                    'close_trades': stats[6],
                    'open_close_ratio': stats[5] / max(stats[6], 1)
                }
                
                # 检查开平仓比例是否合理
                if abs(stats[5] - stats[6]) / max(stats[5], 1) > 0.2:
                    self.integrity_report['integrity_issues'].append(
                        f"开平仓数量不平衡: 开仓{stats[5]}笔, 平仓{stats[6]}笔"
                    )
            
            # 检查补全状态表数据
            completion_sql = """
            SELECT 
                COUNT(*) as total_completions,
                COUNT(CASE WHEN completion_replaced = 1 THEN 1 END) as replaced_count,
                COUNT(DISTINCT member_id) as unique_users_with_completions
            FROM position_completion_status
            """
            
            completion_stats = self.db_manager.execute_query(completion_sql)
            if completion_stats:
                stats = completion_stats[0]
                self.integrity_report['data_sources']['completion_status'] = {
                    'total_completions': stats[0],
                    'replaced_count': stats[1],
                    'unique_users': stats[2]
                }
            
            logger.info("原始数据源检查完成")
            
        except Exception as e:
            logger.error(f"原始数据源检查失败: {str(e)}")
            self.integrity_report['integrity_issues'].append(f"原始数据源检查失败: {str(e)}")
    
    def _check_complete_position_building(self):
        """检查完整订单构建过程"""
        logger.info("检查完整订单构建过程...")
        
        try:
            # 获取最近的交易数据样本
            sample_sql = """
            SELECT position_id, member_id, contract_name, side, deal_vol, deal_vol_usdt, 
                   create_time, deal_price, open_type, liquidity
            FROM position_analysis 
            WHERE create_time >= datetime('now', '-1 days')
            ORDER BY create_time DESC
            LIMIT 10000
            """
            
            raw_data = self.db_manager.execute_query(sample_sql)
            if not raw_data:
                self.integrity_report['integrity_issues'].append("无法获取原始交易数据样本")
                return
            
            # 转换为DataFrame
            df = pd.DataFrame(raw_data, columns=[
                'position_id', 'member_id', 'contract_name', 'side', 'deal_vol', 
                'deal_vol_usdt', 'create_time', 'deal_price', 'open_type', 'liquidity'
            ])
            
            # 使用IncrementalAnalyzer构建完整订单
            analyzer = IncrementalAnalyzer()
            df_processed = analyzer._preprocess_data(df.copy())
            complete_positions = analyzer._build_complete_positions_with_sequence(df_processed, 'test')
            
            # 统计完整订单构建结果
            total_raw_records = len(df)
            total_complete_positions = len(complete_positions)
            completed_positions = sum(1 for pos in complete_positions.values() if pos.is_completed)
            incomplete_positions = total_complete_positions - completed_positions
            
            self.integrity_report['algorithm_data']['complete_position_building'] = {
                'raw_records': total_raw_records,
                'complete_positions': total_complete_positions,
                'completed_positions': completed_positions,
                'incomplete_positions': incomplete_positions,
                'completion_rate': completed_positions / max(total_complete_positions, 1),
                'data_reduction_ratio': total_complete_positions / max(total_raw_records, 1)
            }
            
            # 检查数据丢失情况
            if total_complete_positions < total_raw_records * 0.1:
                self.integrity_report['integrity_issues'].append(
                    f"完整订单构建过程数据丢失严重: 原始{total_raw_records}条 -> 完整订单{total_complete_positions}个"
                )
            
            # 检查必要字段完整性
            missing_fields_count = 0
            for pos in complete_positions.values():
                if not pos.member_id or not pos.contract_name:
                    missing_fields_count += 1
            
            if missing_fields_count > 0:
                self.integrity_report['integrity_issues'].append(
                    f"完整订单中有{missing_fields_count}个缺少必要字段"
                )
            
            # 保存完整订单数据供后续检查使用
            self.complete_positions_sample = complete_positions
            
            logger.info(f"完整订单构建检查完成: {total_raw_records}条原始数据 -> {total_complete_positions}个完整订单")
            
        except Exception as e:
            logger.error(f"完整订单构建检查失败: {str(e)}")
            self.integrity_report['integrity_issues'].append(f"完整订单构建检查失败: {str(e)}")
    
    def _check_wash_trading_data_integrity(self):
        """检查对敲算法数据获取完整性"""
        logger.info("检查对敲算法数据获取完整性...")
        
        try:
            if not hasattr(self, 'complete_positions_sample'):
                self.integrity_report['integrity_issues'].append("缺少完整订单样本数据，无法检查对敲算法")
                return
            
            # 使用PositionBasedOptimizer进行对敲检测
            optimizer = PositionBasedOptimizer()
            optimizer.complete_positions = self.complete_positions_sample
            
            # 执行对敲检测
            wash_trading_results = optimizer.optimized_wash_trading_detection()
            
            # 分析对敲检测结果
            total_positions = len(self.complete_positions_sample)
            wash_trading_count = len(wash_trading_results)
            
            # 统计不同类型的对敲
            same_account_count = sum(1 for r in wash_trading_results 
                                   if r.get('detection_method') == 'same_account_wash_trading')
            cross_account_count = sum(1 for r in wash_trading_results 
                                    if r.get('detection_method') == 'cross_account_wash_trading')
            
            self.integrity_report['algorithm_data']['wash_trading_detection'] = {
                'input_positions': total_positions,
                'detected_wash_trading': wash_trading_count,
                'same_account_wash': same_account_count,
                'cross_account_wash': cross_account_count,
                'detection_rate': wash_trading_count / max(total_positions, 1)
            }
            
            # 检查对敲结果数据完整性
            incomplete_results = 0
            for result in wash_trading_results:
                required_fields = ['member_id', 'contract_name', 'detection_method', 'wash_score']
                missing_fields = [field for field in required_fields if not result.get(field)]
                if missing_fields:
                    incomplete_results += 1
            
            if incomplete_results > 0:
                self.integrity_report['integrity_issues'].append(
                    f"对敲检测结果中有{incomplete_results}条记录缺少必要字段"
                )
            
            logger.info(f"对敲算法数据检查完成: 检测到{wash_trading_count}对可疑对敲交易")

        except Exception as e:
            logger.error(f"对敲算法数据检查失败: {str(e)}")
            self.integrity_report['integrity_issues'].append(f"对敲算法数据检查失败: {str(e)}")

    def _check_high_frequency_data_integrity(self):
        """检查高频算法数据获取完整性"""
        logger.info("检查高频算法数据获取完整性...")

        try:
            if not hasattr(self, 'complete_positions_sample'):
                self.integrity_report['integrity_issues'].append("缺少完整订单样本数据，无法检查高频算法")
                return

            # 使用PositionBasedOptimizer进行高频检测
            optimizer = PositionBasedOptimizer()
            optimizer.complete_positions = self.complete_positions_sample

            # 执行高频检测
            high_freq_results = optimizer.optimized_high_frequency_detection()

            # 分析高频检测结果
            total_positions = len(self.complete_positions_sample)
            high_freq_count = len(high_freq_results)

            # 统计用户和合约分布
            unique_users = len(set(r.get('member_id') for r in high_freq_results if r.get('member_id')))
            unique_contracts = len(set(r.get('contract_name') for r in high_freq_results if r.get('contract_name')))

            self.integrity_report['algorithm_data']['high_frequency_detection'] = {
                'input_positions': total_positions,
                'detected_high_frequency': high_freq_count,
                'unique_users': unique_users,
                'unique_contracts': unique_contracts,
                'detection_rate': high_freq_count / max(total_positions, 1)
            }

            # 检查高频结果数据完整性
            incomplete_results = 0
            for result in high_freq_results:
                required_fields = ['member_id', 'contract_name', 'detection_type', 'total_positions']
                missing_fields = [field for field in required_fields if not result.get(field)]
                if missing_fields:
                    incomplete_results += 1

            if incomplete_results > 0:
                self.integrity_report['integrity_issues'].append(
                    f"高频检测结果中有{incomplete_results}条记录缺少必要字段"
                )

            logger.info(f"高频算法数据检查完成: 检测到{high_freq_count}个高频交易异常")

        except Exception as e:
            logger.error(f"高频算法数据检查失败: {str(e)}")
            self.integrity_report['integrity_issues'].append(f"高频算法数据检查失败: {str(e)}")

    def _check_data_pipeline_integrity(self):
        """检查数据传递链路完整性"""
        logger.info("检查数据传递链路完整性...")

        try:
            # 检查数据流转各个环节
            pipeline_checks = {
                'raw_data_to_complete_positions': self._check_raw_to_complete_conversion(),
                'complete_positions_to_algorithms': self._check_complete_to_algorithm_conversion(),
                'algorithm_results_storage': self._check_algorithm_results_storage()
            }

            self.integrity_report['data_pipeline'] = pipeline_checks

            # 统计管道完整性
            successful_checks = sum(1 for check in pipeline_checks.values() if check.get('status') == 'success')
            total_checks = len(pipeline_checks)
            pipeline_integrity = successful_checks / total_checks

            self.integrity_report['completeness_metrics']['pipeline_integrity'] = pipeline_integrity

            if pipeline_integrity < 0.8:
                self.integrity_report['integrity_issues'].append(
                    f"数据管道完整性较低: {pipeline_integrity:.1%}"
                )

            logger.info(f"数据管道完整性检查完成: {pipeline_integrity:.1%}")

        except Exception as e:
            logger.error(f"数据管道完整性检查失败: {str(e)}")
            self.integrity_report['integrity_issues'].append(f"数据管道完整性检查失败: {str(e)}")

    def _check_raw_to_complete_conversion(self) -> Dict[str, Any]:
        """检查原始数据到完整订单的转换"""
        try:
            if not hasattr(self, 'complete_positions_sample'):
                return {'status': 'failed', 'reason': '缺少完整订单样本'}

            # 检查数据转换率
            raw_data_metrics = self.integrity_report['data_sources'].get('position_analysis', {})
            complete_position_metrics = self.integrity_report['algorithm_data'].get('complete_position_building', {})

            if not raw_data_metrics or not complete_position_metrics:
                return {'status': 'failed', 'reason': '缺少转换指标数据'}

            conversion_rate = complete_position_metrics.get('data_reduction_ratio', 0)
            completion_rate = complete_position_metrics.get('completion_rate', 0)

            return {
                'status': 'success',
                'conversion_rate': conversion_rate,
                'completion_rate': completion_rate,
                'quality_score': (conversion_rate + completion_rate) / 2
            }

        except Exception as e:
            return {'status': 'failed', 'reason': str(e)}

    def _check_complete_to_algorithm_conversion(self) -> Dict[str, Any]:
        """检查完整订单到算法的转换"""
        try:
            wash_metrics = self.integrity_report['algorithm_data'].get('wash_trading_detection', {})
            high_freq_metrics = self.integrity_report['algorithm_data'].get('high_frequency_detection', {})

            if not wash_metrics or not high_freq_metrics:
                return {'status': 'failed', 'reason': '缺少算法检测指标'}

            # 检查算法输入数据一致性
            wash_input = wash_metrics.get('input_positions', 0)
            high_freq_input = high_freq_metrics.get('input_positions', 0)

            if wash_input != high_freq_input:
                return {
                    'status': 'warning',
                    'reason': f'算法输入数据不一致: 对敲{wash_input}, 高频{high_freq_input}',
                    'wash_input': wash_input,
                    'high_freq_input': high_freq_input
                }

            return {
                'status': 'success',
                'consistent_input': True,
                'input_positions': wash_input
            }

        except Exception as e:
            return {'status': 'failed', 'reason': str(e)}

    def _check_algorithm_results_storage(self) -> Dict[str, Any]:
        """检查算法结果存储完整性"""
        try:
            # 检查最近的算法结果存储情况
            storage_sql = """
            SELECT
                COUNT(*) as total_results,
                COUNT(DISTINCT task_id) as unique_tasks,
                MAX(created_at) as latest_result
            FROM algorithm_results
            WHERE created_at >= datetime('now', '-7 days')
            """

            storage_stats = self.db_manager.execute_query(storage_sql)
            if storage_stats:
                stats = storage_stats[0]
                return {
                    'status': 'success',
                    'total_results': stats[0],
                    'unique_tasks': stats[1],
                    'latest_result': stats[2]
                }
            else:
                return {'status': 'failed', 'reason': '无法获取存储统计信息'}

        except Exception as e:
            return {'status': 'failed', 'reason': str(e)}

    def _generate_integrity_assessment(self):
        """生成完整性评估报告"""
        logger.info("生成完整性评估报告...")

        try:
            # 计算总体完整性评分
            metrics = self.integrity_report['completeness_metrics']

            # 数据源完整性评分
            position_data = self.integrity_report['data_sources'].get('position_analysis', {})
            if position_data:
                open_close_balance = 1.0 - abs(position_data.get('open_close_ratio', 1.0) - 1.0)
                metrics['data_source_integrity'] = min(open_close_balance, 1.0)

            # 算法数据完整性评分
            complete_building = self.integrity_report['algorithm_data'].get('complete_position_building', {})
            if complete_building:
                completion_rate = complete_building.get('completion_rate', 0)
                data_reduction = complete_building.get('data_reduction_ratio', 0)
                metrics['algorithm_data_integrity'] = (completion_rate + min(data_reduction * 10, 1.0)) / 2

            # 计算总体评分
            if metrics:
                overall_score = sum(metrics.values()) / len(metrics)
                metrics['overall_integrity_score'] = overall_score

                # 生成建议
                if overall_score >= 0.8:
                    self.integrity_report['recommendations'].append("数据完整性良好，算法可以正常运行")
                elif overall_score >= 0.6:
                    self.integrity_report['recommendations'].append("数据完整性中等，建议优化数据处理流程")
                else:
                    self.integrity_report['recommendations'].append("数据完整性较差，需要重点检查数据源和处理逻辑")

            # 生成具体建议
            if len(self.integrity_report['integrity_issues']) > 5:
                self.integrity_report['recommendations'].append("发现多个数据完整性问题，建议进行全面的数据质量治理")

            logger.info("完整性评估报告生成完成")

        except Exception as e:
            logger.error(f"生成完整性评估失败: {str(e)}")
            self.integrity_report['integrity_issues'].append(f"评估报告生成失败: {str(e)}")

    def print_report(self):
        """打印检查报告"""
        print("\n" + "="*80)
        print("算法数据完整性检查报告")
        print("="*80)

        print(f"\n检查时间: {self.integrity_report['check_time']}")

        # 数据源统计
        print("\n【数据源统计】")
        for source, stats in self.integrity_report['data_sources'].items():
            print(f"  {source}:")
            for key, value in stats.items():
                print(f"    {key}: {value}")

        # 算法数据统计
        print("\n【算法数据统计】")
        for algo, stats in self.integrity_report['algorithm_data'].items():
            print(f"  {algo}:")
            for key, value in stats.items():
                print(f"    {key}: {value}")

        # 完整性指标
        print("\n【完整性指标】")
        for metric, score in self.integrity_report['completeness_metrics'].items():
            print(f"  {metric}: {score:.3f}")

        # 问题列表
        if self.integrity_report['integrity_issues']:
            print("\n【发现的问题】")
            for i, issue in enumerate(self.integrity_report['integrity_issues'], 1):
                print(f"  {i}. {issue}")

        # 建议
        if self.integrity_report['recommendations']:
            print("\n【建议】")
            for i, rec in enumerate(self.integrity_report['recommendations'], 1):
                print(f"  {i}. {rec}")

        print("\n" + "="*80)

def main():
    """主函数"""
    checker = AlgorithmDataIntegrityChecker()
    report = checker.run_comprehensive_check()
    checker.print_report()

    # 保存详细报告到文件
    import json
    with open('temp/algorithm_data_integrity_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"\n详细报告已保存到: temp/algorithm_data_integrity_report.json")

if __name__ == "__main__":
    main()
