#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版算法数据完整性检测工具
直接使用SQL查询检测对敲和高频算法的数据完整性
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Any

try:
    import duckdb
    HAS_DUCKDB = True
except ImportError:
    HAS_DUCKDB = False
    print("警告: 未安装duckdb，尝试使用sqlite3")
    import sqlite3

class SimpleDataIntegrityChecker:
    """简化版数据完整性检测器"""
    
    def __init__(self, db_path='data/risk_analysis.duckdb'):
        self.db_path = db_path
        self.report = {
            'check_time': datetime.now().isoformat(),
            'data_integrity': {},
            'algorithm_readiness': {},
            'issues': [],
            'recommendations': []
        }
    
    def run_check(self) -> Dict[str, Any]:
        """运行数据完整性检查"""
        print("========== 开始算法数据完整性检查 ==========")

        try:
            if HAS_DUCKDB:
                conn = duckdb.connect(self.db_path)
            else:
                conn = sqlite3.connect(self.db_path)

            try:
                # 1. 检查原始交易数据完整性
                self._check_raw_trading_data(conn)

                # 2. 检查补全状态数据
                self._check_completion_status_data(conn)

                # 3. 检查算法结果存储
                self._check_algorithm_results_storage(conn)

                # 4. 检查数据质量指标
                self._check_data_quality_metrics(conn)

                # 5. 评估算法数据准备情况
                self._assess_algorithm_readiness()

            finally:
                conn.close()

            print("========== 数据完整性检查完成 ==========")
            return self.report
            
        except Exception as e:
            print(f"检查过程出错: {str(e)}")
            self.report['issues'].append(f"检查过程出错: {str(e)}")
            return self.report
    
    def _check_raw_trading_data(self, conn):
        """检查用户交易档案数据完整性"""
        print("检查用户交易档案数据完整性...")

        try:
            # 基础统计 - 基于user_trading_profiles表
            if HAS_DUCKDB:
                cursor = conn.cursor()
            else:
                cursor = conn.cursor()

            # 检查原始交易数据 - 使用position_analysis表（全部数据）
            cursor.execute("""
                SELECT
                    COUNT(*) as total_records,
                    COUNT(DISTINCT member_id) as unique_users,
                    COUNT(DISTINCT contract_name) as unique_contracts,
                    COUNT(CASE WHEN primary_side = 1 THEN 1 END) as long_positions,
                    COUNT(CASE WHEN primary_side = 3 THEN 1 END) as short_positions,
                    MIN(open_time) as earliest_time,
                    MAX(open_time) as latest_time
                FROM position_analysis
            """)
            
            stats = cursor.fetchone()
            if stats:
                total_records, users, contracts, longs, shorts, earliest, latest = stats

                self.report['data_integrity']['position_analysis'] = {
                    'total_records': total_records,
                    'unique_users': users,
                    'unique_contracts': contracts,
                    'long_positions': longs,
                    'short_positions': shorts,
                    'time_range': f"{earliest} to {latest}",
                    'long_short_ratio': longs / max(shorts, 1) if shorts > 0 else 0
                }

                # 检查数据量是否充足
                if total_records < 1000:
                    self.report['issues'].append(f"持仓数据量较少: {total_records}条")

                # 检查多空平衡
                if shorts > 0 and abs(longs - shorts) / max(longs, 1) > 0.8:
                    self.report['issues'].append(
                        f"多空持仓数量严重不平衡: 多头{longs}个, 空头{shorts}个"
                    )

                print(f"  - 总持仓记录数: {total_records:,}")
                print(f"  - 用户数: {users:,}")
                print(f"  - 合约数: {contracts}")
                print(f"  - 多头/空头: {longs:,}/{shorts:,}")
                print(f"  - 时间范围: {earliest} to {latest}")
            
            # 检查数据质量（全部数据）
            cursor.execute("""
                SELECT
                    COUNT(CASE WHEN member_id IS NULL OR member_id = '' THEN 1 END) as null_member_id,
                    COUNT(CASE WHEN contract_name IS NULL OR contract_name = '' THEN 1 END) as null_contract,
                    COUNT(CASE WHEN total_open_amount IS NULL OR total_open_amount <= 0 THEN 1 END) as invalid_open_amount,
                    COUNT(CASE WHEN total_close_amount IS NULL OR total_close_amount <= 0 THEN 1 END) as invalid_close_amount,
                    COUNT(CASE WHEN primary_side NOT IN (1, 2, 3, 4) THEN 1 END) as invalid_side
                FROM position_analysis
            """)
            
            quality_stats = cursor.fetchone()
            if quality_stats:
                null_member, null_contract, invalid_open, invalid_close, invalid_side = quality_stats

                self.report['data_integrity']['data_quality'] = {
                    'null_member_id': null_member,
                    'null_contract': null_contract,
                    'invalid_open_amount': invalid_open,
                    'invalid_close_amount': invalid_close,
                    'invalid_side': invalid_side
                }

                # 检查数据质量问题
                total_issues = sum(quality_stats)
                if total_issues > 0:
                    self.report['issues'].append(f"发现{total_issues}条数据质量问题")

                print(f"  - 空用户ID: {null_member}")
                print(f"  - 空合约名: {null_contract}")
                print(f"  - 无效开仓金额: {invalid_open}")
                print(f"  - 无效平仓金额: {invalid_close}")
                print(f"  - 无效方向: {invalid_side}")
            
        except Exception as e:
            self.report['issues'].append(f"原始交易数据检查失败: {str(e)}")
            print(f"  错误: {str(e)}")
    
    def _check_completion_status_data(self, conn):
        """检查交易完整性数据"""
        print("检查交易完整性数据...")

        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT
                    COUNT(*) as total_profiles,
                    AVG(completed_positions) as avg_completed_positions,
                    AVG(total_positions) as avg_total_positions,
                    COUNT(CASE WHEN completed_positions > 0 THEN 1 END) as users_with_completed,
                    COUNT(CASE WHEN total_positions > completed_positions THEN 1 END) as users_with_incomplete
                FROM user_trading_profiles
            """)

            stats = cursor.fetchone()
            if stats:
                total, avg_completed, avg_total, users_completed, users_incomplete = stats

                completion_rate = float(avg_completed) / max(float(avg_total), 1) if avg_total else 0

                self.report['data_integrity']['trading_completeness'] = {
                    'total_profiles': total,
                    'avg_completed_positions': float(avg_completed) if avg_completed else 0,
                    'avg_total_positions': float(avg_total) if avg_total else 0,
                    'users_with_completed': users_completed,
                    'users_with_incomplete': users_incomplete,
                    'completion_rate': completion_rate
                }

                print(f"  - 用户档案数: {total}")
                print(f"  - 平均完成持仓数: {avg_completed:.1f}" if avg_completed else "  - 平均完成持仓数: 0")
                print(f"  - 平均总持仓数: {avg_total:.1f}" if avg_total else "  - 平均总持仓数: 0")
                print(f"  - 完成率: {completion_rate:.1%}")

        except Exception as e:
            self.report['issues'].append(f"交易完整性数据检查失败: {str(e)}")
            print(f"  错误: {str(e)}")
    
    def _check_algorithm_results_storage(self, conn):
        """检查算法结果存储"""
        print("检查算法结果存储...")

        try:
            cursor = conn.cursor()

            # 检查对敲交易检测结果
            cursor.execute("""
                SELECT
                    COUNT(*) as total_cross_account_wash,
                    COUNT(DISTINCT user_a_id) as unique_users_cross,
                    SUM(trade_a_volume) as total_cross_volume
                FROM cross_account_wash_trading
            """)

            cross_wash_stats = cursor.fetchone()
            if cross_wash_stats:
                total_cross, users_cross, vol_cross = cross_wash_stats

                # 检查同账户对敲
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_same_account_wash,
                        COUNT(DISTINCT user_id) as unique_users_same,
                        SUM(long_volume) as total_same_volume
                    FROM same_account_wash_trading
                """)

                same_wash_stats = cursor.fetchone()
                total_same, users_same, vol_same = same_wash_stats if same_wash_stats else (0, 0, 0)

                self.report['data_integrity']['wash_trading_analysis'] = {
                    'cross_account_wash_count': total_cross,
                    'cross_account_users': users_cross,
                    'cross_account_volume': float(vol_cross) if vol_cross else 0,
                    'same_account_wash_count': total_same,
                    'same_account_users': users_same,
                    'same_account_volume': float(vol_same) if vol_same else 0,
                    'total_wash_count': total_cross + total_same,
                    'total_wash_users': users_cross + users_same
                }

                print(f"  - 跨账户对敲: {total_cross:,}条, {users_cross}用户")
                print(f"  - 同账户对敲: {total_same:,}条, {users_same}用户")
                print(f"  - 总对敲检测: {total_cross + total_same:,}条")

            # 检查高频交易数据
            cursor.execute("""
                SELECT
                    COUNT(*) as total_high_freq,
                    COUNT(DISTINCT user_id) as unique_users_hf,
                    SUM(trade_count) as total_hf_trades
                FROM high_frequency_trading_details
            """)

            hf_stats = cursor.fetchone()
            if hf_stats:
                total_hf, users_hf, trades_hf = hf_stats
                self.report['data_integrity']['high_frequency_analysis'] = {
                    'high_frequency_count': total_hf,
                    'high_frequency_users': users_hf,
                    'high_frequency_trades': int(trades_hf) if trades_hf else 0
                }
                print(f"  - 高频交易检测: {total_hf:,}条, {users_hf}用户")
                print(f"  - 高频交易总笔数: {trades_hf}")

        except Exception as e:
            self.report['issues'].append(f"算法结果存储检查失败: {str(e)}")
            print(f"  错误: {str(e)}")
    
    def _check_data_quality_metrics(self, conn):
        """检查数据质量指标"""
        print("检查数据质量指标...")

        try:
            cursor = conn.cursor()

            # 检查用户交易行为分布
            cursor.execute("""
                SELECT
                    member_id,
                    total_trades,
                    total_volume,
                    win_rate,
                    wash_trading_volume,
                    high_frequency_volume,
                    abnormal_volume,
                    professional_score
                FROM user_trading_profiles
                WHERE total_trades > 0
                ORDER BY total_trades DESC
                LIMIT 20
            """)

            active_users = cursor.fetchall()
            if active_users:
                # 分析活跃用户的交易模式
                high_activity_users = [user for user in active_users if user[1] > 100]
                wash_trading_users = [user for user in active_users if user[4] and user[4] > 0]
                high_freq_users = [user for user in active_users if user[5] and user[5] > 0]
                abnormal_users = [user for user in active_users if user[6] and user[6] > 0]

                self.report['data_integrity']['user_behavior_analysis'] = {
                    'active_users_count': len(active_users),
                    'high_activity_users': len(high_activity_users),
                    'wash_trading_users': len(wash_trading_users),
                    'high_frequency_users': len(high_freq_users),
                    'abnormal_volume_users': len(abnormal_users),
                    'top_user_trades': active_users[0][1] if active_users else 0
                }

                print(f"  - 活跃用户数: {len(active_users)}")
                print(f"  - 高交易量用户数: {len(high_activity_users)}")
                print(f"  - 对敲交易用户数: {len(wash_trading_users)}")
                print(f"  - 高频交易用户数: {len(high_freq_users)}")
                print(f"  - 异常交易用户数: {len(abnormal_users)}")

        except Exception as e:
            self.report['issues'].append(f"数据质量指标检查失败: {str(e)}")
            print(f"  错误: {str(e)}")
    
    def _assess_algorithm_readiness(self):
        """评估算法数据准备情况"""
        print("评估算法数据准备情况...")
        
        try:
            # 对敲算法准备情况评估
            position_data = self.report['data_integrity'].get('position_analysis', {})
            wash_data = self.report['data_integrity'].get('wash_trading_analysis', {})
            data_quality = self.report['data_integrity'].get('data_quality', {})

            wash_readiness = {
                'data_volume_sufficient': position_data.get('total_records', 0) >= 10000,
                'user_diversity_good': position_data.get('unique_users', 0) >= 1000,
                'wash_detection_results_exist': wash_data.get('total_wash_count', 0) > 0,
                'data_quality_acceptable': sum(data_quality.values()) < position_data.get('total_records', 1) * 0.1
            }

            wash_score = sum(wash_readiness.values()) / len(wash_readiness)

            # 高频算法准备情况评估
            hf_data = self.report['data_integrity'].get('high_frequency_analysis', {})

            high_freq_readiness = {
                'data_volume_sufficient': position_data.get('total_records', 0) >= 10000,
                'user_diversity_good': position_data.get('unique_users', 0) >= 1000,
                'high_freq_detection_results_exist': hf_data.get('high_frequency_count', 0) > 0,
                'data_quality_acceptable': sum(data_quality.values()) < position_data.get('total_records', 1) * 0.1
            }

            high_freq_score = sum(high_freq_readiness.values()) / len(high_freq_readiness)
            
            self.report['algorithm_readiness'] = {
                'wash_trading': {
                    'readiness_score': wash_score,
                    'details': wash_readiness,
                    'status': 'ready' if wash_score >= 0.8 else 'needs_improvement' if wash_score >= 0.6 else 'not_ready'
                },
                'high_frequency': {
                    'readiness_score': high_freq_score,
                    'details': high_freq_readiness,
                    'status': 'ready' if high_freq_score >= 0.8 else 'needs_improvement' if high_freq_score >= 0.6 else 'not_ready'
                }
            }
            
            # 生成建议
            if wash_score < 0.8:
                self.report['recommendations'].append("对敲算法数据准备不充分，建议检查数据完整性和补全逻辑")
            
            if high_freq_score < 0.8:
                self.report['recommendations'].append("高频算法数据准备不充分，建议增加活跃用户数据或优化数据收集")
            
            if len(self.report['issues']) > 5:
                self.report['recommendations'].append("发现多个数据质量问题，建议进行全面的数据清理")
            
            print(f"  - 对敲算法准备度: {wash_score:.1%}")
            print(f"  - 高频算法准备度: {high_freq_score:.1%}")
            
        except Exception as e:
            self.report['issues'].append(f"算法准备情况评估失败: {str(e)}")
            print(f"  错误: {str(e)}")
    
    def print_report(self):
        """打印检查报告"""
        print("\n" + "="*80)
        print("算法数据完整性检查报告")
        print("="*80)
        
        print(f"\n检查时间: {self.report['check_time']}")
        
        # 数据完整性统计
        print("\n【数据完整性统计】")
        for category, stats in self.report['data_integrity'].items():
            print(f"  {category}:")
            if isinstance(stats, dict):
                for key, value in stats.items():
                    print(f"    {key}: {value}")
            else:
                print(f"    {stats}")
        
        # 算法准备情况
        print("\n【算法准备情况】")
        for algo, readiness in self.report['algorithm_readiness'].items():
            status = readiness.get('status', 'unknown')
            score = readiness.get('readiness_score', 0)
            print(f"  {algo}: {status} (评分: {score:.1%})")
        
        # 发现的问题
        if self.report['issues']:
            print("\n【发现的问题】")
            for i, issue in enumerate(self.report['issues'], 1):
                print(f"  {i}. {issue}")
        
        # 建议
        if self.report['recommendations']:
            print("\n【建议】")
            for i, rec in enumerate(self.report['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print("\n" + "="*80)

def main():
    """主函数"""
    checker = SimpleDataIntegrityChecker()
    report = checker.run_check()
    checker.print_report()
    
    # 保存详细报告
    with open('temp/simple_data_integrity_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细报告已保存到: temp/simple_data_integrity_report.json")

if __name__ == "__main__":
    main()
