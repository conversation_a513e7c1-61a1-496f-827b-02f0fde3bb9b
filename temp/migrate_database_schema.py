#!/usr/bin/env python3
"""
数据库架构迁移脚本
为修复数据丢失问题，扩展现有表结构
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def migrate_cross_account_wash_trading_table():
    """迁移跨账户对敲交易表结构"""
    print("🔧 开始迁移cross_account_wash_trading表结构...")
    
    try:
        from backend.database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager()
        
        # 检查表是否存在
        check_table_sql = """
        SELECT COUNT(*) as table_count 
        FROM information_schema.tables 
        WHERE table_name = 'cross_account_wash_trading'
        """
        
        result = db_manager.execute_sql(check_table_sql)
        table_exists = result and result[0][0] > 0
        
        if table_exists:
            print("✅ 表已存在，检查字段...")
            
            # 检查新字段是否存在
            check_columns_sql = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'cross_account_wash_trading'
            """
            
            columns_result = db_manager.execute_sql(check_columns_sql)
            existing_columns = [row[0] for row in columns_result] if columns_result else []
            
            print(f"📋 现有字段: {existing_columns}")
            
            # 需要添加的新字段
            new_columns = [
                ('pair_index', 'INTEGER DEFAULT 1'),
                ('trade_a_open_time', 'TIMESTAMP'),
                ('trade_a_close_time', 'TIMESTAMP'),
                ('trade_a_volume', 'DECIMAL(20,8)'),
                ('trade_a_profit', 'DECIMAL(20,8)'),
                ('trade_b_open_time', 'TIMESTAMP'),
                ('trade_b_close_time', 'TIMESTAMP'),
                ('trade_b_volume', 'DECIMAL(20,8)'),
                ('trade_b_profit', 'DECIMAL(20,8)'),
                ('open_time_diff_seconds', 'INTEGER DEFAULT 0'),
                ('close_time_diff_seconds', 'INTEGER DEFAULT 0'),
                ('total_amount', 'DECIMAL(20,8)'),
                ('net_profit', 'DECIMAL(20,8)'),
                ('profit_hedge_score', 'DECIMAL(10,6) DEFAULT 0'),
                ('wash_score', 'DECIMAL(10,6) DEFAULT 0'),
                ('trade_pair_detail_json', 'TEXT')
            ]
            
            # 添加缺失的字段
            for column_name, column_type in new_columns:
                if column_name not in existing_columns:
                    try:
                        alter_sql = f"ALTER TABLE cross_account_wash_trading ADD COLUMN {column_name} {column_type}"
                        db_manager.execute_sql(alter_sql)
                        print(f"✅ 添加字段: {column_name}")
                    except Exception as e:
                        print(f"⚠️ 添加字段 {column_name} 失败: {e}")
                else:
                    print(f"⏭️ 字段已存在: {column_name}")
        else:
            print("📋 表不存在，将在下次初始化时创建")
        
        print("✅ cross_account_wash_trading表迁移完成")
        return True
        
    except Exception as e:
        print(f"❌ 表迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def backup_existing_data():
    """备份现有数据"""
    print("💾 开始备份现有数据...")
    
    try:
        from backend.database.duckdb_manager import DuckDBManager
        
        db_manager = DuckDBManager()
        
        # 备份关键表的数据
        tables_to_backup = [
            'algorithm_results',
            'contract_risk_details',
            'cross_account_wash_trading',
            'wash_trading_results'
        ]
        
        backup_info = {}
        
        for table_name in tables_to_backup:
            try:
                count_sql = f"SELECT COUNT(*) FROM {table_name}"
                result = db_manager.execute_sql(count_sql)
                count = result[0][0] if result else 0
                backup_info[table_name] = count
                print(f"📊 {table_name}: {count} 条记录")
            except Exception as e:
                print(f"⚠️ 无法访问表 {table_name}: {e}")
                backup_info[table_name] = 'N/A'
        
        # 保存备份信息
        backup_file = f"temp/backup_info_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        import json
        with open(backup_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'tables': backup_info
            }, f, indent=2)
        
        print(f"✅ 备份信息已保存到: {backup_file}")
        return True
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def verify_migration():
    """验证迁移结果"""
    print("🔍 验证迁移结果...")
    
    try:
        from backend.database.algorithm_storage_manager import AlgorithmStorageManager
        
        storage_manager = AlgorithmStorageManager()
        
        # 尝试初始化表结构
        if storage_manager.initialize_tables():
            print("✅ 表结构初始化成功")
            
            # 检查新表结构
            with storage_manager.db.get_connection() as conn:
                # 检查cross_account_wash_trading表结构
                columns_sql = """
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'cross_account_wash_trading'
                ORDER BY ordinal_position
                """
                
                try:
                    columns = conn.execute(columns_sql).fetchall()
                    print(f"📋 cross_account_wash_trading表字段 ({len(columns)}个):")
                    for column_name, data_type in columns:
                        print(f"  - {column_name}: {data_type}")
                    
                    # 检查关键字段是否存在
                    required_fields = [
                        'profit_hedge_score', 'wash_score', 'trade_pair_detail_json',
                        'total_amount', 'net_profit'
                    ]
                    
                    existing_fields = [col[0] for col in columns]
                    missing_fields = [field for field in required_fields if field not in existing_fields]
                    
                    if missing_fields:
                        print(f"⚠️ 缺少关键字段: {missing_fields}")
                        return False
                    else:
                        print("✅ 所有关键字段都存在")
                        return True
                        
                except Exception as e:
                    print(f"⚠️ 无法检查表结构: {e}")
                    return False
        else:
            print("❌ 表结构初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始数据库架构迁移")
    print("=" * 50)
    
    # 1. 备份现有数据
    if not backup_existing_data():
        print("❌ 备份失败，停止迁移")
        return False
    
    # 2. 迁移表结构
    if not migrate_cross_account_wash_trading_table():
        print("❌ 表结构迁移失败")
        return False
    
    # 3. 验证迁移结果
    if not verify_migration():
        print("❌ 迁移验证失败")
        return False
    
    print("\n🎉 数据库架构迁移完成！")
    print("=" * 50)
    print("📋 迁移总结:")
    print("✅ 现有数据已备份")
    print("✅ cross_account_wash_trading表结构已扩展")
    print("✅ 新字段已添加:")
    print("  - profit_hedge_score: 盈亏对冲评分")
    print("  - wash_score: 综合对敲评分")
    print("  - trade_pair_detail_json: 交易对详情")
    print("  - total_amount: 总交易金额")
    print("  - net_profit: 净盈亏")
    print("  - 以及其他详细字段...")
    print("\n⚠️ 注意事项:")
    print("- 现有数据的新字段将使用默认值")
    print("- 建议重新运行分析以获取完整数据")
    print("- 如有问题，可以参考备份信息进行恢复")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 迁移失败，请检查错误信息并重试")
        sys.exit(1)
    else:
        print("\n✅ 迁移成功完成！")
