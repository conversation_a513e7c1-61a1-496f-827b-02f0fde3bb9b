#!/usr/bin/env python3
"""
简单的修复测试脚本
测试数据存储和检索是否正常工作
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'backend'))

def test_storage_manager():
    """测试存储管理器"""
    print("🧪 测试存储管理器...")
    
    try:
        from backend.database.algorithm_storage_manager import AlgorithmStorageManager
        
        storage_manager = AlgorithmStorageManager()
        
        # 初始化表结构
        print("📋 初始化表结构...")
        if storage_manager.initialize_tables():
            print("✅ 表结构初始化成功")
        else:
            print("❌ 表结构初始化失败")
            return False
        
        # 创建测试数据
        test_task_id = f"test_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        test_result_data = {
            'contract_risks': [
                {
                    'member_id': 'test_user_a',
                    'contract_name': 'BTC_USDT',
                    'detection_type': 'suspected_wash_trading',
                    'detection_method': 'cross_account_wash_trading',
                    'risk_level': 'High',
                    'risk_score': 0.85,
                    'abnormal_volume': 1000.0,
                    'trade_count': 2,
                    'time_range': '2025-07-29 22:00',
                    'counterparty_ids': ['test_user_b'],
                    'additional_data': {
                        'profit_hedge_score': 0.95,
                        'wash_score': 0.88,
                        'total_profit': -5.0,
                        'user_a_profit': 50.0,
                        'user_b_profit': -55.0,
                        'trade_pair_detail': {
                            'pair_index': 1,
                            'user_a': {
                                'member_id': 'test_user_a',
                                'open_time': '2025-07-29T22:00:00',
                                'open_amount': 1000.0,
                                'profit': 50.0
                            },
                            'user_b': {
                                'member_id': 'test_user_b',
                                'open_time': '2025-07-29T22:00:05',
                                'open_amount': 1020.0,
                                'profit': -55.0
                            },
                            'time_gaps': {
                                'open_gap_seconds': 5,
                                'close_gap_seconds': 120
                            },
                            'total_amount': 2020.0,
                            'net_profit': -5.0
                        }
                    }
                }
            ],
            'summary': {
                'total_analyzed': 100,
                'risks_found': 1
            }
        }
        
        # 测试存储
        print("💾 测试数据存储...")
        result_id = storage_manager.store_algorithm_result(
            task_id=test_task_id,
            algorithm_type='suspected_wash_trading',
            result_data=test_result_data
        )
        
        if result_id:
            print(f"✅ 数据存储成功，result_id: {result_id}")
            
            # 测试检索
            print("🔍 测试数据检索...")
            retrieved_result = storage_manager.query_algorithm_results(test_task_id)
            
            if retrieved_result:
                print("✅ 数据检索成功")
                
                # 检查数据完整性
                contract_risks = retrieved_result.get('contract_risks', [])
                if contract_risks:
                    risk = contract_risks[0]
                    print(f"📊 检索到的数据:")
                    print(f"  - member_id: {risk.get('member_id')}")
                    print(f"  - abnormal_volume: {risk.get('abnormal_volume')}")
                    print(f"  - profit_hedge_score: {risk.get('profit_hedge_score')}")
                    print(f"  - wash_score: {risk.get('wash_score')}")
                    print(f"  - trade_pair_detail: {bool(risk.get('trade_pair_detail'))}")
                    
                    # 检查关键数据是否丢失
                    issues = []
                    if risk.get('abnormal_volume') == '0 USDT':
                        issues.append("异常交易量为0")
                    if not risk.get('profit_hedge_score'):
                        issues.append("缺少盈亏对冲评分")
                    if not risk.get('trade_pair_detail'):
                        issues.append("缺少交易对详情")
                    
                    if issues:
                        print(f"⚠️ 发现问题: {', '.join(issues)}")
                        return False
                    else:
                        print("✅ 数据完整性检查通过")
                        return True
                else:
                    print("❌ 没有检索到风险数据")
                    return False
            else:
                print("❌ 数据检索失败")
                return False
        else:
            print("❌ 数据存储失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始简单修复测试")
    print("=" * 40)
    
    success = test_storage_manager()
    
    if success:
        print("\n🎉 修复测试通过！")
        print("=" * 40)
        print("✅ 数据存储和检索正常工作")
        print("✅ 数据完整性得到保证")
        print("✅ 可以开始使用修复后的系统")
    else:
        print("\n❌ 修复测试失败")
        print("=" * 40)
        print("请检查错误信息并重新运行测试")
    
    return success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
